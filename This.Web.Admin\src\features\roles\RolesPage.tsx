import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { AgGridReact } from 'ag-grid-react';
import type { ColDef, GridApi, GridReadyEvent } from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { RefreshCw, Shield, AlertCircle } from 'lucide-react';
import { Button } from '@/shared/components/atoms/Button/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/molecules/Card/Card';
import { cn } from '@/shared/utils/utils';
import { ApiService, type Role } from '../../services/apiService';

interface RolesPageProps {
  className?: string;
}

export const RolesPage: React.FC<RolesPageProps> = ({ className }) => {
  // State management
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);

  // Get tenant ID from localStorage (following existing pattern)
  const getCurrentTenant = (): string => {
    return localStorage.getItem('selectedTenantId') || 'kitchsync'; // fallback to default
  };

  // API service instance
  const apiService = ApiService.getInstance();

  // Fetch roles data
  const fetchRoles = useCallback(async (useCache: boolean = true) => {
    try {
      setLoading(true);
      setError(null);
      
      const tenantId = getCurrentTenant();
      const rolesData = await apiService.getRoles(tenantId, useCache);
      
      setRoles(rolesData);
    } catch (err) {
      console.error('Error fetching roles:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch roles');
    } finally {
      setLoading(false);
    }
  }, [apiService]);

  // Initial data load
  useEffect(() => {
    fetchRoles();
  }, [fetchRoles]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    fetchRoles(false); // Force refresh without cache
  }, [fetchRoles]);

  // Grid ready handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api);

    // Auto-size columns when grid is ready
    setTimeout(() => {
      params.api.sizeColumnsToFit();
    }, 100);
  }, []);

  // Simple Badge component
  const Badge = ({ children, variant = "default", className = "" }: {
    children: React.ReactNode;
    variant?: "default" | "success" | "destructive" | "secondary" | "outline";
    className?: string;
  }) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium";
    const variantClasses = {
      default: "bg-primary/10 text-primary",
      success: "bg-green-100 text-green-800",
      destructive: "bg-red-100 text-red-800",
      secondary: "bg-gray-100 text-gray-800",
      outline: "border border-gray-300 text-gray-700"
    };

    return (
      <span className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
        {children}
      </span>
    );
  };

  // Role name renderer with badge
  const RoleNameRenderer = ({ value }: { value: string }) => {
    return (
      <div className="flex items-center gap-2">
        <Shield className="h-4 w-4 text-primary" />
        <span className="font-medium">{value}</span>
      </div>
    );
  };

  // Description renderer with truncation
  const DescriptionRenderer = ({ value }: { value: string }) => {
    if (!value) {
      return <span className="text-muted-foreground italic">No description</span>;
    }
    
    const maxLength = 100;
    const truncated = value.length > maxLength ? `${value.substring(0, maxLength)}...` : value;
    
    return (
      <span 
        className="text-sm"
        title={value.length > maxLength ? value : undefined}
      >
        {truncated}
      </span>
    );
  };

  // Role ID renderer with copy functionality
  const RoleIdRenderer = ({ value }: { value: string }) => {
    const handleCopy = async () => {
      try {
        await navigator.clipboard.writeText(value);
        // Could add a toast notification here
      } catch (err) {
        console.error('Failed to copy role ID:', err);
      }
    };

    return (
      <div className="flex items-center gap-2">
        <code className="text-xs bg-muted px-2 py-1 rounded font-mono">
          {value.substring(0, 8)}...
        </code>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy}
          className="h-6 w-6 p-0 hover:bg-muted"
          title="Copy full ID"
        >
          📋
        </Button>
      </div>
    );
  };

  // Column definitions
  const columnDefs: ColDef[] = useMemo(() => [
    {
      headerName: 'Role Name',
      field: 'name',
      sortable: true,
      filter: true,
      pinned: 'left',
      width: 200,
      cellRenderer: RoleNameRenderer
    },
    {
      headerName: 'Description',
      field: 'description',
      sortable: true,
      filter: true,
      width: 400,
      cellRenderer: DescriptionRenderer,
      flex: 1 // Take remaining space
    },
    {
      headerName: 'Role ID',
      field: 'id',
      sortable: true,
      filter: true,
      width: 150,
      cellRenderer: RoleIdRenderer
    }
  ], []);

  return (
    <div className={cn('space-y-6 p-6', className)}>
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Shield className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-foreground">Roles ({roles.length})</h1>
            <p className="text-sm text-muted-foreground">
              Manage and view system roles
            </p>
          </div>
        </div>
        
        <Button
          onClick={handleRefresh}
          disabled={loading}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-1 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Roles Grid */}
          <div className="ag-theme-alpine" style={{ height: '600px', width: '100%' }}>
            <AgGridReact
              rowData={roles}
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              loading={loading}
              
              // Grid configuration
              defaultColDef={{
                sortable: true,
                filter: true,
                resizable: true,
                minWidth: 100
              }}
              
              // Pagination
              pagination={true}
              paginationPageSize={20}
              paginationPageSizeSelector={[10, 20, 50, 100]}
              
              // Other features
              animateRows={true}
              suppressCellFocus={true}
              rowSelection="multiple"
              
              // Styling
              headerHeight={40}
              rowHeight={50}
              
              // Row ID for better performance
              getRowId={(params) => params.data.id}
            />
          </div>
    </div>
  );
};
