// Entity Types
export interface BaseEntity {
  id: string;
  isActive: boolean;
  showSequence: number;
  createdAt: string;
  createdBy?: string;
  modifiedAt?: string;
  modifiedBy?: string;
}

export interface Product extends BaseEntity {
  name: string;
  description?: string;
  features?: Feature[];
}

export interface Feature extends BaseEntity {
  productId: string;
  customerId?: string;
  name: string;
  description?: string;
  isDefault: boolean;
  objects?: ObjectEntity[];
}

export interface ObjectEntity extends BaseEntity {
  featureId: string;
  customerId?: string;
  parentId?: string;
  name: string;
  description?: string;
  isRoot: boolean;
  children?: ObjectEntity[];
  status?: ObjectStatus[];
  actions?: ObjectAction[];
}

export interface User extends BaseEntity {
  fullName: string;
  username: string;
  designation: string;
  department: string;
  roles: string[];
}

export interface Role extends BaseEntity {
  name: string;
  description: string;
  permissions?: Permission[];
}

export interface Permission {
  id: string;
  name: string;
  description?: string;
  actionId?: string;
  isDefault: boolean;
  isGranted: boolean;
}

export interface Template extends BaseEntity {
  name: string;
  description?: string;
  type: string;
}

export interface ObjectStatus extends BaseEntity {
  objectId: string;
  customerId?: string;
  name: string;
  description?: string;
}

export interface ObjectAction extends BaseEntity {
  objectId: string;
  actionId: string;
  customerId?: string;
}

export interface Action extends BaseEntity {
  name: string;
  description?: string;
  isDefault: boolean;
}

export interface Metadata extends BaseEntity {
  name: string;
  dataType: string;
  dataTypeId?: string;
  validationRules?: Record<string, unknown>;
  isRequired: boolean;
  isRestricted: boolean;
  description?: string;
  customerId?: string;
}

export interface DataType extends BaseEntity {
  name: string;
  description: string;
  validationRules: Record<string, unknown>;
}

export interface ProductMetadata extends BaseEntity {
  productId: string;
  metadataId: string;
  value: string;
}

export interface FeatureMetadata extends BaseEntity {
  featureId: string;
  metadataId: string;
  value: string;
}

export interface ObjectMetadata extends BaseEntity {
  objectId: string;
  metadataId: string;
  value: string;
}

// UI Types
export interface Column<T> {
  key: keyof T | string;
  header: string;
  width?: string;
  render?: (item: T) => React.ReactNode;
}

export interface TableProps<T> {
  data: T[];
  columns: Column<T>[];
  isSelectable?: boolean;
  selectedItems?: string[];
  onSelect?: (ids: string[]) => void;
  onSelectAll?: (selected: boolean) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}