import React, { useState, useCallback } from 'react';
import { ChevronDown, ChevronRight, Folder, Folder2Open as FolderOpen, Plus, Download } from 'react-bootstrap-icons';
import Button from 'react-bootstrap/Button';
import './TreeView.css';

type NodeType = 'product' | 'role' | 'object' | 'action' | 'permission';

const NODE_TYPE_COLORS: Record<NodeType, string> = {
  product: '#FFD700', // Gold
  role: '#198754',   // Green
  object: '#fd7e14', // Orange
  action: '#20c997', // Light Green
  permission: '#0d6e0d' // Dark Green
};

const NODE_TYPE_LABELS: Record<NodeType, string> = {
  product: 'Product',
  role: 'Role',
  object: 'Object',
  action: 'Action',
  permission: 'Permission'
};

const NODE_HIERARCHY: Record<NodeType, NodeType[]> = {
  product: ['object', 'role'],
  role: [],
  object: ['object', 'action', 'permission'],
  action: [],
  permission: []
};

export interface TreeNode {
  id: string; // Internal ID for UI management only
  name: string;
  type: NodeType;
  children?: TreeNode[];
  isOpen?: boolean;
  metadata?: Record<string, any>;
  [key: string]: any; // Allow dynamic properties
}

export interface TreeViewProps {
  data: TreeNode[];
  selectedNode: TreeNode | null;
  onSelect: (node: TreeNode) => void;
  onAddFolder: (parentId: string, name: string, type: NodeType) => void;
}

// Helper function to get node color styles
const getNodeColor = (type: NodeType) => ({
  borderLeft: `3px solid ${NODE_TYPE_COLORS[type]}`,
  backgroundColor: `${NODE_TYPE_COLORS[type]}15`
});

// Memoize the node component to prevent unnecessary re-renders
interface TreeNodeComponentProps {
  node: TreeNode;
  level: number;
  isSelected: boolean;
  onSelect: (node: TreeNode) => void;
  onAddClick: (e: React.MouseEvent, node: TreeNode) => void;
  onAddFolder: (e: React.FormEvent, parentId: string) => void;
  selectedNode: TreeNode | null;
  addingFolderId: string | null;
  isAdding: boolean;
  newFolderName: string;
  setNewFolderName: (name: string) => void;
  newNodeType: NodeType;
  setNewNodeType: (type: NodeType) => void;
  setIsAdding: (isAdding: boolean) => void;
  setAddingFolderId: (id: string | null) => void;
  expandedNodes: Record<string, boolean>;
  toggleNode: (node: TreeNode) => void;
}

const TreeNodeComponent = React.memo<TreeNodeComponentProps>(({ 
  node, 
  level, 
  isSelected, 
  onSelect, 
  onAddClick, 
  onAddFolder, 
  selectedNode, 
  addingFolderId, 
  isAdding, 
  newFolderName, 
  setNewFolderName, 
  newNodeType, 
  setNewNodeType, 
  setIsAdding, 
  setAddingFolderId,
  expandedNodes,
  toggleNode
}) => {
  const hasChildren = node.children && node.children.length > 0;
  const isOpen = expandedNodes[node.id] ?? (node.isOpen ?? false);
  const nodeType = node.type || 'object';
  const canAddChildren = NODE_HIERARCHY[nodeType]?.length > 0;

  return (
    <div className="tree-node">
      <div 
        className={`node-content ${isSelected ? 'selected' : ''}`}
        style={{
          ...getNodeColor(node.type),
          paddingLeft: `${level * 0.75}rem`,
        }}
        onClick={() => onSelect({ ...node, isOpen: !isOpen })}
      >
        <div className="node-name d-flex align-items-center">
          {hasChildren ? (
            <span 
              className="me-1" 
              onClick={(e) => {
                e.stopPropagation();
                toggleNode(node);
              }}
            >
              {isOpen ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
            </span>
          ) : (
            <span className="me-1" style={{ width: '14px' }}></span>
          )}
          <span className="me-1">
            {isOpen ? <FolderOpen size={16} /> : <Folder size={16} />}
          </span>
          <span className="flex-grow-1">{node.name}</span>
          <span 
            className="badge" 
            style={{ 
              backgroundColor: NODE_TYPE_COLORS[node.type],
              color: node.type === 'product' ? '#000' : '#fff'
            }}
          >
            {NODE_TYPE_LABELS[node.type]}
          </span>
          {canAddChildren && (
            <Button 
              variant="link" 
              size="sm" 
              className="p-0 ms-2"
              onClick={(e) => onAddClick(e, node)}
              title={`Add ${NODE_TYPE_LABELS[NODE_HIERARCHY[node.type][0]]}`}
            >
              <Plus size={14} />
            </Button>
          )}
        </div>
      </div>
      
      {addingFolderId === node.id && isAdding && (
        <div className="add-folder-form">
          <form onSubmit={(e) => onAddFolder(e, node.id)}>
            <div className="form-row">
              <input
                type="text"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder="Enter name..."
                autoFocus
                onClick={(e) => e.stopPropagation()}
                required
              />
              <select 
                value={newNodeType}
                onChange={(e) => setNewNodeType(e.target.value as NodeType)}
                onClick={(e) => e.stopPropagation()}
              >
                {NODE_HIERARCHY[node.type]?.map(type => (
                  <option key={type} value={type}>
                    {NODE_TYPE_LABELS[type]}
                  </option>
                ))}
              </select>
            </div>
            <div className="button-group">
              <button 
                type="button"
                onClick={() => {
                  setAddingFolderId(null);
                  setIsAdding(false);
                }}
              >
                Cancel
              </button>
              <button 
                type="submit"
                disabled={!newFolderName.trim()}
              >
                Add
              </button>
            </div>
          </form>
        </div>
      )}
      
      {isOpen && hasChildren && (
        <div className="node-children">
          {node.children?.map(childNode => (
            <TreeNodeComponent 
              key={childNode.id}
              node={childNode}
              level={level + 1}
              isSelected={selectedNode?.id === childNode.id}
              onSelect={onSelect}
              onAddClick={onAddClick}
              onAddFolder={onAddFolder}
              selectedNode={selectedNode}
              addingFolderId={addingFolderId}
              isAdding={isAdding}
              newFolderName={newFolderName}
              setNewFolderName={setNewFolderName}
              newNodeType={newNodeType}
              setNewNodeType={setNewNodeType}
              setIsAdding={setIsAdding}
              setAddingFolderId={setAddingFolderId}
              expandedNodes={expandedNodes}
              toggleNode={toggleNode}
            />
          ))}
        </div>
      )}
    </div>
  );
});

export const TreeView: React.FC<TreeViewProps> = ({ data, selectedNode, onSelect, onAddFolder }) => {
  const [expandedNodes, setExpandedNodes] = useState<Record<string, boolean>>({});

  const toggleNode = useCallback((node: TreeNode) => {
    setExpandedNodes(prev => ({
      ...prev,
      [node.id]: !prev[node.id]
    }));
  }, []);
  const [newFolderName, setNewFolderName] = useState('');
  const [addingFolderId, setAddingFolderId] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [newNodeType, setNewNodeType] = useState<NodeType>('object');

  const handleExportClick = useCallback(() => {
    // Function to get the child property name based on node type and parent type
    const getChildPropertyName = (type: NodeType, parentType?: NodeType): string => {
      // If parent is a product, children should be objects or roles
      if (parentType === 'product') {
        if (type === 'object') return 'objects';
        if (type === 'role') return 'roles';
      }

      // For other cases, use type-specific names
      switch (type) {
        case 'product': return 'products';
        case 'object': return 'objects';
        case 'role': return 'roles';
        case 'action': return 'actions';
        case 'permission': return 'permissions';
        default: return 'children';
      }
    };

    // Function to recursively process all nodes with proper hierarchy
    const processNode = (node: TreeNode): any => {
      const result: any = {
        name: node.name,
        type: node.type
      };

      // Add metadata if present (clean internal IDs and unwanted fields)
      if (node.metadata) {
        if (Array.isArray(node.metadata)) {
          result.metadata = node.metadata.map(item => {
            // Remove _internalId and id fields, keep only essential fields
            const cleanItem: any = {
              name: item.name,
              type: item.type,
              description: item.description,
              required: item.required,
              isActive: item.isActive
            };

            // Only add defaultValue if it exists
            if (item.defaultValue !== undefined && item.defaultValue !== null && item.defaultValue !== '') {
              cleanItem.defaultValue = item.defaultValue;
            }

            return cleanItem;
          });
        } else {
          result.metadata = node.metadata;
        }
      }
      
      // Process children if they exist
      if (node.children && node.children.length > 0) {
        // Process each child and group by type
        const childrenByType: Record<string, any[]> = {};
        
        node.children.forEach(child => {
          const propertyName = getChildPropertyName(child.type as NodeType, node.type);
          
          if (!childrenByType[propertyName]) {
            childrenByType[propertyName] = [];
          }
          
          // For objects, include their actions and permissions directly
          if (child.type === 'object') {
            const childNode = processNode(child);
            childrenByType[propertyName].push(childNode);
          } 
          // For actions and permissions, add them directly to their parent object
          else if (child.type === 'action' || child.type === 'permission') {
            // These will be handled by their parent object
            return;
          }
          // For other types, process normally with parent type
          else {
            childrenByType[propertyName].push(processNode(child));
          }
        });
        
        // Add each group to the result
        Object.entries(childrenByType).forEach(([propertyName, children]) => {
          if (children.length > 0) {
            result[propertyName] = children;
          }
        });
      }
      
      // For objects, we don't need to include actions and permissions in the simplified structure
      // The desired JSON structure only has products with objects, no nested actions/permissions
      
      return result;
    };
    
    // Process the entire tree structure - simplified to match desired JSON format
    const products = data.filter(node => node.type === 'product').map(product => {
      const productData = processNode(product);

      // Add objects as a direct property (only objects, no roles/actions/permissions)
      const objects = product.children
        ?.filter(child => child.type === 'object')
        .map(obj => processNode(obj)) || [];

      // Always include objects array, even if empty
      productData.objects = objects;

      return productData;
    });

    const exportData = {
      products
    };
    
    // Clean up empty arrays and internal properties from the result
    const cleanExportData = (obj: any): any => {
      if (Array.isArray(obj)) {
        return obj.map(cleanExportData);
      } else if (obj !== null && typeof obj === 'object') {
        const result: any = {};
        for (const key in obj) {
          // Skip internal properties
          if (key === 'isOpen' || key === 'parent' || key === 'id' || key === '_internalId') continue;

          const value = cleanExportData(obj[key]);
          // Only include non-empty arrays and non-undefined values
          if (value !== undefined && !(Array.isArray(value) && value.length === 0)) {
            result[key] = value;
          }
        }
        return result;
      }
      return obj;
    };
    
    // Clean the export data and convert to JSON string
    const cleanedData = cleanExportData(exportData);
    const jsonString = JSON.stringify(cleanedData, null, 2);
    
    // Create a Blob with the JSON data
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    // Create a temporary anchor element and trigger the download
    const a = document.createElement('a');
    a.href = url;
    a.download = 'product-structure.json';
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 0);
  }, [data]);

  // Set initial node type based on parent type
  const getInitialNodeType = useCallback((parentType: NodeType | 'root'): NodeType => {
    if (parentType === 'root') return 'product';
    const allowedTypes = NODE_HIERARCHY[parentType as NodeType];
    return allowedTypes?.[0] || 'object';
  }, []);

  const handleAddClick = useCallback((e: React.MouseEvent, node: TreeNode) => {
    e.stopPropagation();
    setAddingFolderId(node.id);
    setNewFolderName('');
    // Get the initial node type based on the parent type
    const initialType = getInitialNodeType(node.type);
    setNewNodeType(initialType);
    setIsAdding(true);
  }, [getInitialNodeType]);

  const handleAddFolder = useCallback((e: React.FormEvent, parentId: string) => {
    e.preventDefault();
    if (!newFolderName.trim()) return;
    
    // Ensure we have a valid node type
    const nodeTypeToAdd = newNodeType || 'object';
    onAddFolder(parentId, newFolderName.trim(), nodeTypeToAdd);
    setNewFolderName('');
    setAddingFolderId(null);
    setIsAdding(false);
  }, [newFolderName, newNodeType, onAddFolder]);

  const handleAddRootClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setAddingFolderId('root');
    setNewFolderName('');
    setNewNodeType('product');
    setIsAdding(true);
  }, []);
  
  const getNodeColor = (type: NodeType) => ({
    borderLeft: `3px solid ${NODE_TYPE_COLORS[type]}`,
    backgroundColor: `${NODE_TYPE_COLORS[type]}15`
  });

  const renderNode = (node: TreeNode, level = 0) => {
    const hasChildren = node.children && node.children.length > 0;
    const isSelected = selectedNode?.id === node.id;
    const isOpen = node.isOpen ?? false;
    const nodeType = node.type || 'object';
    const canAddChildren = NODE_HIERARCHY[nodeType]?.length > 0;

    return (
      <div key={node.id} className="tree-node">
        <div 
          className={`node-content ${isSelected ? 'selected' : ''}`}
          style={{
            paddingLeft: `${level * 16 + 8}px`,
            ...getNodeColor(nodeType)
          }}
          onClick={() => onSelect(node)}
        >
          <div className="d-flex align-items-center">
            {hasChildren ? (
              <Button 
                variant="link" 
                size="sm" 
                className="p-0 me-1 text-muted"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleNode(node);
                }}
              >
                {isOpen ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
              </Button>
            ) : (
              <div style={{ width: '24px' }}></div>
            )}
            
            {isOpen ? <FolderOpen className="me-2" /> : <Folder className="me-2" />}
            
            <span className="node-name">
              {node.name}
              <span 
                className="badge ms-2" 
                style={{
                  backgroundColor: NODE_TYPE_COLORS[nodeType],
                  color: nodeType === 'product' ? '#000' : '#fff',
                  fontSize: '0.65em',
                  padding: '0.2em 0.4em'
                }}
              >
                {NODE_TYPE_LABELS[nodeType]}
              </span>
            </span>
            
            {canAddChildren && (
              <div className="ms-auto">
                <Button 
                  variant="link" 
                  size="sm" 
                  className="p-0 text-muted"
                  onClick={(e) => handleAddClick(e, node)}
                  title={`Add ${NODE_HIERARCHY[nodeType][0]}`}
                >
                  <Plus size={14} />
                </Button>
              </div>
            )}
          </div>
          
          {addingFolderId === node.id && isAdding && (
            <div className="add-folder-form">
              <form onSubmit={(e) => handleAddFolder(e, node.id)}>
                <div className="form-row">
                  <input
                    type="text"
                    value={newFolderName}
                    onChange={(e) => setNewFolderName(e.target.value)}
                    placeholder="Enter name..."
                    autoFocus
                    onClick={(e) => e.stopPropagation()}
                    required
                  />
                  <select 
                    value={newNodeType}
                    onChange={(e) => {
                      e.stopPropagation();
                      setNewNodeType(e.target.value as NodeType);
                    }}
                    className="form-select form-select-sm ms-2"
                    style={{ width: 'auto' }}
                  >
                    {NODE_HIERARCHY[node.type]?.map(type => (
                      <option key={type} value={type}>
                        {NODE_TYPE_LABELS[type] || type}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="button-group">
                  <button 
                    type="button"
                    onClick={() => {
                      setAddingFolderId(null);
                      setIsAdding(false);
                    }}
                  >
                    Cancel
                  </button>
                  <button 
                    type="submit"
                    disabled={!newFolderName.trim()}
                  >
                    Add
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>
        
        {isOpen && hasChildren && (
          <div className="node-children">
            {node.children?.map(childNode => renderNode(childNode, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="tree-view d-flex flex-column h-100">
      <div className="tree-header d-flex justify-content-between align-items-center p-2 border-bottom">
        <span>Admin Panel</span>
        <div className="d-flex align-items-center">
          <Button
            variant="link"
            size="sm"
            className="p-0 me-2"
            onClick={handleExportClick}
            title="Export product structure"
          >
            <Download size={16} />
          </Button>
          <Button
            variant="link"
            size="sm"
            className="p-0"
            onClick={handleAddRootClick}
            title="Add product"
          >
            <Plus size={16} />
          </Button>
        </div>
      </div>
      <div className="tree-content p-2 flex-grow-1" style={{ overflowY: 'auto' }}>
        {data.length === 0 && !isAdding && (
          <div className="d-flex flex-column align-items-center justify-content-center text-muted" style={{ height: '100%' }}>
            <div className="mb-2">No products yet</div>
            <Button
              variant="outline-primary"
              size="sm"
              onClick={handleAddRootClick}
            >
              <Plus size={14} className="me-1" /> Add Your First Product
            </Button>
          </div>
        )}
        {addingFolderId === 'root' && isAdding && (
          <div className="add-folder-form">
            <form onSubmit={(e) => handleAddFolder(e, 'root')}>
              <div className="form-row">
                <input
                  type="text"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  placeholder="Product name"
                  autoFocus
                  required
                />
                <select
                  value={newNodeType}
                  onChange={(e) => setNewNodeType(e.target.value as NodeType)}
                >
                  <option value="product">Product</option>
                </select>
              </div>
              <div className="button-group">
                <button
                  type="button"
                  onClick={() => {
                    setAddingFolderId(null);
                    setIsAdding(false);
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!newFolderName.trim()}
                >
                  Add
                </button>
              </div>
            </form>
          </div>
        )}
        {data.map(node => (
          <TreeNodeComponent 
            key={node.id}
            node={node}
            level={0}
            isSelected={selectedNode?.id === node.id}
            onSelect={onSelect}
            onAddClick={handleAddClick}
            onAddFolder={handleAddFolder}
            selectedNode={selectedNode}
            addingFolderId={addingFolderId}
            isAdding={isAdding}
            newFolderName={newFolderName}
            setNewFolderName={setNewFolderName}
            newNodeType={newNodeType}
            setNewNodeType={setNewNodeType}
            setIsAdding={setIsAdding}
            setAddingFolderId={setAddingFolderId}
            expandedNodes={expandedNodes}
            toggleNode={toggleNode}
          />
        ))}
      </div>
      <div className="tree-footer p-2 border-top">
        <div className="d-flex flex-wrap gap-2">
          {Object.entries(NODE_TYPE_LABELS).map(([type, label]) => (
            <div key={type} className="legend-item">
              <span 
                className="legend-color" 
                style={{ backgroundColor: NODE_TYPE_COLORS[type as NodeType] }}
              />
              {label}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Export the NodeType for use in other components
export type { NodeType };
