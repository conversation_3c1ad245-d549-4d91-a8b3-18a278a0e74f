import { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, Badge, Button, Modal, Form } from 'react-bootstrap';
import { TreeView, type TreeNode, type NodeType } from './components/TreeView/TreeView';
import { DataGrid, type Column } from 'react-data-grid';
import 'react-data-grid/lib/styles.css';
import { useLocation, useNavigate } from 'react-router-dom';
import { templateService, TemplateValidator } from './services/templateService';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

interface RenderCellProps {
  row: any;
  onRowChange?: (row: any) => void;
}

interface RenderEditCellProps {
  row: any;
  column: any;
  onRowChange: (row: any) => void;
  onClose: (commitChanges?: boolean) => void;
}

// Available data types
const DATA_TYPES = [
  'Rating', 'Day', 'Time', 'Color', 'Textarea', 'Multiselect', 'Text', 'Url',
  'Boolean', 'Currency', 'Month', 'Select', 'File', 'Richtext', 'Image', 'Year',
  'Radio', 'Tag', 'Number', 'Datetime', 'Phone', 'Slider', 'Checkbox',
  'Percentage', 'Address', 'Email', 'Date', 'Guid'
];



// Base metadata type
type BaseMetadata = {
  name: string;
  type: string;
  description: string;
  required: boolean;
  defaultValue?: string;
  isActive: boolean;
  // Internal ID for UI management only - not saved to template
  _internalId?: string;
};

// Specific metadata types
type ProductMetadata = BaseMetadata & {
  name: 'Name' | 'Description' | 'Version' | 'IsActive';
};



type ObjectMetadata = BaseMetadata & {
  name: 'ParentObjectId' | 'Name' | 'Description' | 'IsActive';
};

type RoleMetadata = BaseMetadata & {
  name: 'Name' | 'Description' | 'IsActive';
};

type ActionMetadata = BaseMetadata & {
  name: 'Name' | 'Description' | 'IsActive';
};

type PermissionMetadata = BaseMetadata & {
  name: 'Name' | 'Description' | 'IsActive';
};

type MetadataItem = ProductMetadata | ObjectMetadata | RoleMetadata | ActionMetadata | PermissionMetadata;

// Helper function to generate hexadecimal GUID
const generateGuid = (): string => {
  // Generate a proper hexadecimal GUID in format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
  const hex = () => Math.floor(Math.random() * 16).toString(16);
  const s4 = () => hex() + hex() + hex() + hex();
  const s8 = () => s4() + s4();
  const s12 = () => s4() + s4() + s4();

  // Generate UUID v4 format
  const uuid = [
    s8(),                    // 8 hex digits
    s4(),                    // 4 hex digits
    '4' + hex() + hex() + hex(), // 4 hex digits starting with '4'
    (8 + Math.floor(Math.random() * 4)).toString(16) + hex() + hex() + hex(), // 4 hex digits starting with 8, 9, A, or B
    s12()                    // 12 hex digits
  ].join('-');

  return uuid.toUpperCase(); // Return in uppercase hexadecimal
};

// Default metadata for each type
const DEFAULT_PRODUCT_METADATA: ProductMetadata[] = [
  { name: 'Name', type: 'Text', description: 'Product name', required: true, isActive: true, _internalId: generateGuid() },
  { name: 'Description', type: 'Textarea', description: 'Product description', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'Version', type: 'Text', description: 'Product version', required: true, isActive: true, defaultValue: '1.0.0', _internalId: generateGuid() },
  { name: 'IsActive', type: 'Boolean', description: 'Whether the product is active', required: true, isActive: true, defaultValue: 'true', _internalId: generateGuid() },
];



const DEFAULT_OBJECT_METADATA: ObjectMetadata[] = [
  { name: 'ParentObjectId', type: 'Text', description: 'Parent object identifier', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'Name', type: 'Text', description: 'Object name', required: true, isActive: true, _internalId: generateGuid() },
  { name: 'Description', type: 'Textarea', description: 'Object description', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'IsActive', type: 'Boolean', description: 'Whether the object is active', required: true, isActive: true, defaultValue: 'true', _internalId: generateGuid() },
];

const DEFAULT_ROLE_METADATA: RoleMetadata[] = [
  { name: 'Name', type: 'Text', description: 'Role name', required: true, isActive: true, _internalId: generateGuid() },
  { name: 'Description', type: 'Textarea', description: 'Role description', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'IsActive', type: 'Boolean', description: 'Whether the role is active', required: true, isActive: true, defaultValue: 'true', _internalId: generateGuid() },
];

const DEFAULT_ACTION_METADATA: ActionMetadata[] = [
  { name: 'Name', type: 'Text', description: 'Action name', required: true, isActive: true, _internalId: generateGuid() },
  { name: 'Description', type: 'Textarea', description: 'Action description', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'IsActive', type: 'Boolean', description: 'Whether the action is active', required: true, isActive: true, defaultValue: 'true', _internalId: generateGuid() },
];

const DEFAULT_PERMISSION_METADATA: PermissionMetadata[] = [
  { name: 'Name', type: 'Text', description: 'Permission name', required: true, isActive: true, _internalId: generateGuid() },
  { name: 'Description', type: 'Textarea', description: 'Permission description', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'IsActive', type: 'Boolean', description: 'Whether the permission is active', required: true, isActive: true, defaultValue: 'true', _internalId: generateGuid() },
];



// Get default metadata based on node type
const getDefaultMetadata = (nodeType: string): MetadataItem[] => {
  switch (nodeType) {
    case 'product':
      return [...DEFAULT_PRODUCT_METADATA];
    case 'object':
      return [...DEFAULT_OBJECT_METADATA];
    case 'role':
      return [...DEFAULT_ROLE_METADATA];
    case 'action':
      return [...DEFAULT_ACTION_METADATA];
    case 'permission':
      return [...DEFAULT_PERMISSION_METADATA];
    default:
      return [];
  }
};

function App() {
  const location = useLocation();
  const navigate = useNavigate();
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [tempMetadata, setTempMetadata] = useState<MetadataItem[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [templateInfo, setTemplateInfo] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [saveFormData, setSaveFormData] = useState({
    templateName: '',
    version: '*******',
    stage: 'draft' as 'draft' | 'beta' | 'live' | 'archived',
    description: ''
  });
  const [originalStage, setOriginalStage] = useState<string | null>(null);



  // Convert tree data to template JSON format
  const convertTreeDataToTemplate = () => {
    // Helper function to clean metadata by removing internal IDs and any other unwanted fields
    const cleanMetadata = (metadata: any) => {
      if (!Array.isArray(metadata)) return [];
      return metadata.map(item => {
        // Remove _internalId and id fields, keep only the essential fields
        const cleanItem: any = {
          name: item.name,
          type: item.type,
          description: item.description,
          required: item.required,
          isActive: item.isActive
        };

        // Only add defaultValue if it exists
        if (item.defaultValue !== undefined && item.defaultValue !== null && item.defaultValue !== '') {
          cleanItem.defaultValue = item.defaultValue;
        }

        return cleanItem;
      });
    };

    const templateJson = {
      products: treeData.map(product => ({
        name: product.name,
        type: product.type,
        metadata: cleanMetadata(product.metadata || []),
        objects: product.children?.filter(child => child.type === 'object').map(obj => ({
          name: obj.name,
          type: obj.type,
          metadata: cleanMetadata(obj.metadata || [])
        })) || []
      }))
    };
    return templateJson;
  };

  // Show save template modal
  const handleShowSaveModal = () => {
    // Initialize form with current template info or defaults
    const currentStage = templateInfo?.stage || 'draft';

    // Get template name from existing template or from the first product
    let defaultTemplateName = '';
    if (templateInfo?.name) {
      defaultTemplateName = templateInfo.name;
    } else if (treeData.length > 0) {
      defaultTemplateName = treeData[0].name;
    }

    setSaveFormData({
      templateName: defaultTemplateName,
      version: templateInfo?.version || '*******',
      stage: currentStage,
      description: templateInfo?.description || ''
    });

    // Track the original stage to detect changes
    setOriginalStage(currentStage);

    setShowSaveModal(true);
  };

  // Save template function
  const handleSaveTemplate = async () => {
    setIsSaving(true);

    try {
      // If no tree data exists, create a minimal template structure
      let templateJson;
      if (treeData.length === 0) {
        // Get product name from existing template or use default
        const productName = templateInfo?.templateJson?.products?.[0]?.name || 'Unnamed Product';
        templateJson = {
          products: [
            {
              name: productName,
              type: "product",
              metadata: [],
              objects: []
            }
          ]
        };
      } else {
        templateJson = convertTreeDataToTemplate();
      }

      // Validate template data
      const validationErrors = TemplateValidator.validateTemplateJson(templateJson);
      if (validationErrors.length > 0) {
        toast.error(`Validation errors:\n${validationErrors.join('\n')}`);
        return;
      }

      // Determine if this is a new template, status change, or update
      const isNewTemplate = !templateInfo?.id || templateInfo.id.startsWith('template-');
      const isStatusChange = !isNewTemplate && originalStage && originalStage !== saveFormData.stage;

      let result;
      if (isNewTemplate || isStatusChange) {
        // Create new template for new templates OR when status changes
        result = await templateService.createTemplate({
          name: saveFormData.templateName,
          version: saveFormData.version,
          stage: saveFormData.stage,
          templateJson: typeof templateJson === 'string' ? templateJson : JSON.stringify(templateJson), // Send as string
          isActive: true
        });
      } else {
        // Update existing template only when keeping the same status
        result = await templateService.updateTemplate(templateInfo.id, {
          name: saveFormData.templateName,
          version: saveFormData.version,
          stage: saveFormData.stage,
          templateJson: typeof templateJson === 'string' ? templateJson : JSON.stringify(templateJson), // Send as string
          isActive: true
        });
      }

      // Update template info with new version and create tree data if it was empty
      setTemplateInfo((prev: any) => ({
        ...prev,
        name: saveFormData.templateName,
        version: saveFormData.version,
        stage: saveFormData.stage,
        description: saveFormData.description,
        publishedAt: new Date().toISOString(),
        id: result.id || prev?.id
      }));

      // If tree was empty, populate it with the saved template
      if (treeData.length === 0) {
        const newTreeData = templateJson.products.map((product: any) =>
          convertTemplateToTreeNode(product)
        );
        setTreeData(newTreeData);
      }

      // Determine the action for the success message
      let actionText = 'saved';
      if (isNewTemplate) {
        actionText = 'created';
      } else if (isStatusChange) {
        actionText = `created with new status (${saveFormData.stage})`;
      } else {
        actionText = 'updated';
      }
      toast.success(`Template ${actionText} successfully! Version: ${saveFormData.version}`);
      setHasUnsavedChanges(false);
      setShowSaveModal(false);

    } catch (error) {
      console.error('Error saving template:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast.error(`Error saving template: ${errorMessage}`);
    } finally {
      setIsSaving(false);
    }
  };

  // Load template data if provided
  useEffect(() => {
    const templateData = location.state?.templateData;
    if (templateData && templateData.templateJson?.products) {
      // Store template info
      setTemplateInfo(templateData);

      // Set the original stage for tracking status changes
      setOriginalStage(templateData.stage || 'draft');

      // Convert template JSON to tree data
      const convertedTreeData = templateData.templateJson.products.map((product: any) =>
        convertTemplateToTreeNode(product)
      );
      setTreeData(convertedTreeData);
    } else {
      // If no template data provided, start with empty tree
      setTreeData([]);
      setTemplateInfo(null);
      setOriginalStage(null);
    }
  }, [location.state]);

  // Convert template product to TreeNode
  const convertTemplateToTreeNode = (templateProduct: any): TreeNode => {
    // Helper function to add internal IDs to metadata
    const addInternalIds = (metadata: any[]) => {
      return metadata.map(item => ({
        ...item,
        _internalId: generateGuid()
      }));
    };

    const node: TreeNode = {
      id: generateGuid(), // Generate new ID for UI management
      name: templateProduct.name,
      type: templateProduct.type as NodeType,
      children: [],
      isOpen: true,
      metadata: addInternalIds(templateProduct.metadata || [])
    };

    // Convert objects to children
    if (templateProduct.objects) {
      node.children = templateProduct.objects.map((obj: any) => ({
        id: generateGuid(), // Generate new ID for UI management
        name: obj.name,
        type: obj.type as NodeType,
        children: obj.children ? obj.children.map((child: any) => convertTemplateToTreeNode(child)) : [],
        isOpen: true,
        metadata: addInternalIds(obj.metadata || [])
      }));
    }

    // Convert roles to children
    if (templateProduct.roles) {
      const roleChildren = templateProduct.roles.map((role: any) => ({
        id: generateGuid(), // Generate new ID for UI management
        name: role.name,
        type: role.type as NodeType,
        children: [],
        isOpen: true,
        metadata: addInternalIds(role.metadata || [])
      }));
      node.children = [...(node.children || []), ...roleChildren];
    }

    return node;
  };

  // Helper function to update a specific row in tempMetadata
  const updateTempMetadataRow = useCallback((rowId: string, updates: Partial<MetadataItem>) => {
    setTempMetadata(prev => {
      const updatedRows = prev.map(row =>
        row._internalId === rowId ? { ...row, ...updates } : row
      );
      return updatedRows;
    });
    setHasUnsavedChanges(true);
  }, []);

  // Custom text editor that updates tempMetadata
  const CustomTextEditor = ({ row, column, onRowChange, onClose }: RenderEditCellProps) => {
    const [value, setValue] = useState(row[column.key] || '');

    const handleSave = () => {
      updateTempMetadataRow(row._internalId, { [column.key]: value });
      onRowChange({ ...row, [column.key]: value });
      onClose(true);
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
      if (event.key === 'Enter') {
        handleSave();
      } else if (event.key === 'Escape') {
        onClose(false);
      }
    };

    const handleBlur = () => {
      handleSave();
    };

    return (
      <input
        type="text"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        autoFocus
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          outline: 'none',
          padding: '8px',
          fontSize: 'inherit',
          fontFamily: 'inherit'
        }}
      />
    );
  };

  // Custom type selector that updates tempMetadata
  const CustomTypeEditor = ({ row, column, onRowChange, onClose }: RenderEditCellProps) => {
    const [value, setValue] = useState(row[column.key] || 'Text');

    const handleSave = () => {
      updateTempMetadataRow(row._internalId, { [column.key]: value });
      onRowChange({ ...row, [column.key]: value });
      onClose(true);
    };

    const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
      const newValue = event.target.value;
      setValue(newValue);
      updateTempMetadataRow(row._internalId, { [column.key]: newValue });
      onRowChange({ ...row, [column.key]: newValue });
      onClose(true);
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
      if (event.key === 'Enter') {
        handleSave();
      } else if (event.key === 'Escape') {
        onClose(false);
      }
    };

    return (
      <select
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        autoFocus
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          outline: 'none',
          padding: '8px',
          fontSize: 'inherit',
          fontFamily: 'inherit',
          backgroundColor: 'white'
        }}
      >
        {DATA_TYPES.map(type => (
          <option key={type} value={type}>
            {type}
          </option>
        ))}
      </select>
    );
  };

  const metadataColumns: Column<MetadataItem>[] = [
    {
      key: 'name',
      name: 'Name',
      renderEditCell: CustomTextEditor,
    },
    {
      key: 'type',
      name: 'Type',
      renderEditCell: CustomTypeEditor,
    },
    {
      key: 'description',
      name: 'Description',
      renderEditCell: CustomTextEditor,
    },
    {
      key: 'required',
      name: 'Required',
      renderCell: (props: any) => (
        <input
          type="checkbox"
          checked={props.row.required}
          onChange={(e) => {
            updateTempMetadataRow(props.row._internalId, { required: e.target.checked });
          }}
          style={{
            margin: '8px'
          }}
        />
      ),
    },
    {
      key: 'defaultValue',
      name: 'Default Value',
      renderEditCell: CustomTextEditor,
    },
    {
      key: 'isActive',
      name: 'Active',
      renderCell: (props: any) => (
        <input
          type="checkbox"
          checked={props.row.isActive}
          onChange={(e) => {
            updateTempMetadataRow(props.row._internalId, { isActive: e.target.checked });
          }}
          style={{
            margin: '8px'
          }}
        />
      ),
    },
    {
      key: 'actions',
      name: 'Actions',
      renderCell: (props: RenderCellProps) => (
        <div className="d-flex gap-1">
          <Button
            variant="link"
            size="sm"
            className="text-danger p-0"
            onClick={() => {
              if (!selectedNode?.type) return;

              // Remove from temp metadata
              const updatedTempRows = tempMetadata.filter((item: any) => item._internalId !== props.row._internalId);
              setTempMetadata(updatedTempRows);
              setHasUnsavedChanges(true);
            }}
          >
            Remove
          </Button>
        </div>
      ),
    },
  ];
  const [selectedNode, setSelectedNode] = useState<TreeNode | null>(null);
  
  const handleSelect = useCallback((node: TreeNode) => {
    setSelectedNode(node);
    setHasUnsavedChanges(false);
    if (node.type) {
      // Use the node's metadata if it exists, otherwise use default metadata
      const nodeMetadata = Array.isArray(node.metadata) && node.metadata.length > 0
        ? node.metadata
        : getDefaultMetadata(node.type);



      // Initialize temporary metadata with current node metadata
      setTempMetadata([...nodeMetadata]);
    }
  }, []);
  
  const findNode = useCallback((nodes: TreeNode[], id: string): { node: TreeNode, parent: TreeNode | null, index: number } | null => {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].id === id) {
        return { node: nodes[i], parent: null, index: i };
      }
      if (nodes[i].children) {
        const found = findNode(nodes[i].children || [], id);
        if (found) {
          return {
            ...found,
            parent: found.parent === null ? nodes[i] : found.parent
          };
        }
      }
    }
    return null;
  }, []);
  
  const addFolder = useCallback((parentId: string, name: string, type: NodeType) => {
    const newId = generateGuid();

    // Get default metadata for this node type
    const defaultMetadata = getDefaultMetadata(type);

    const newFolder: TreeNode = {
      id: newId,
      name,
      type,
      children: [],
      isOpen: true,
      // Attach default metadata to the node
      metadata: [...defaultMetadata] // Create a new array to avoid reference issues
    };

    // Mark as having unsaved changes
    setHasUnsavedChanges(true);

    setTreeData(prevData => {
      // Create a deep copy of the previous data
      const data = JSON.parse(JSON.stringify(prevData));

      // If adding to root
      if (parentId === 'root') {
        return [...data, newFolder];
      }
      
      // Function to find the parent node and add the new folder
      const findAndAdd = (nodes: TreeNode[]): boolean => {
        for (let i = 0; i < nodes.length; i++) {
          // If this is the parent node, add the new folder
          if (nodes[i].id === parentId) {
            if (!nodes[i].children) {
              nodes[i].children = [];
            }
            nodes[i].children!.push(newFolder);
            // Make sure parent is expanded to show the new child
            nodes[i].isOpen = true;
            return true;
          }
          
          // Recursively search in children
          if (nodes[i].children && nodes[i].children!.length > 0) {
            if (findAndAdd(nodes[i].children!)) {
              return true;
            }
          }
        }
        return false;
      };
      
      // Start the search from the root
      findAndAdd(data);
      
      // Return the updated tree
      return [...data];
    });
  }, []);
  
  const handleAddMetadata = useCallback(() => {
    if (!selectedNode?.type) return;

    // Create a new metadata item with a default name based on the node type
    const defaultName = `new_${selectedNode.type}_${Date.now()}`;

    const newItem: MetadataItem = {
      _internalId: generateGuid(),
      name: defaultName as any, // Type assertion since we know the name will be valid
      type: 'Text',
      description: 'Custom field',
      required: false,
      isActive: true
    };

    // Add to temporary metadata
    setTempMetadata(prev => [...prev, newItem]);
    setHasUnsavedChanges(true);
  }, [selectedNode]);

  const handleSaveMetadata = useCallback(() => {
    if (!selectedNode?.type) return;

    // Update the selected node's metadata
    const updatedNode = {
      ...selectedNode,
      metadata: [...tempMetadata]
    };

    // Update the tree data with the new node
    const updateNodeInTree = (nodes: TreeNode[]): boolean => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === selectedNode.id) {
          nodes[i] = updatedNode;
          return true;
        }

        if (nodes[i].children && nodes[i].children!.length > 0) {
          if (updateNodeInTree(nodes[i].children!)) {
            return true;
          }
        }
      }
      return false;
    };

    setTreeData(prevData => {
      const newData = [...prevData];
      updateNodeInTree(newData);
      return newData;
    });



    // Update the selected node to ensure the UI updates
    setSelectedNode(updatedNode);
    setHasUnsavedChanges(false);
  }, [selectedNode, tempMetadata]);
  
  return (
    <div className="d-flex flex-column min-vh-100">
      {/* Header */}
      <header className="bg-dark text-white py-3 shadow">
        <Container>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              <h1 className="h3 mb-0 me-3">Admin Panel</h1>
              {templateInfo && (
                <div className="d-flex align-items-center">
                  <Badge bg="primary" className="me-2">
                    {templateInfo.templateJson?.products?.[0]?.name || 'Template'}
                  </Badge>
                  <Badge bg="secondary" className="me-2">
                    v{templateInfo.version}
                  </Badge>
                  <Badge bg={templateInfo.stage === 'live' ? 'success' : 'warning'}>
                    {templateInfo.stage?.toUpperCase()}
                  </Badge>
                </div>
              )}
            </div>
            <div className="d-flex align-items-center gap-2">
              {templateInfo && (
                <small className="text-light opacity-75">
                  Mode: {location.state?.mode || 'edit'}
                </small>
              )}
              <Button
                variant="success"
                size="sm"
                onClick={handleShowSaveModal}
                disabled={isSaving}
                className="d-flex align-items-center gap-1"
              >
                💾
                {templateInfo?.id && !templateInfo.id.startsWith('template-') ? 'Update Template' : 'Save Template'}
              </Button>
              <Button
                variant="outline-light"
                size="sm"
                onClick={() => navigate('/templates')}
              >
                Back to Templates
              </Button>
            </div>
          </div>
        </Container>
      </header>

      {/* Main Content */}
      <main className="flex-grow-1 py-4">
        <Container fluid className="px-0">
          <Row className="g-4">
            {/* Left Pane - 4/12 */}
            <Col md={4} className="order-2 order-md-1 ps-4 pe-2" style={{ paddingLeft: '20px', paddingTop: '20px', paddingBottom: '20px' }}>
              <div className="bg-white rounded shadow-sm h-100 d-flex flex-column" style={{ minHeight: '70vh' }}>
                <TreeView
                  data={treeData}
                  selectedNode={selectedNode}
                  onSelect={handleSelect}
                  onAddFolder={addFolder}
                />
              </div>
            </Col>

            {/* Right Pane - 8/12 */}
            <Col md={8} className="order-1 order-md-2 ps-2 pe-4" style={{ paddingRight: '20px', paddingTop: '20px', paddingBottom: '20px' }}>
              <div className="bg-white rounded shadow-sm h-100 d-flex flex-column" style={{ minHeight: '70vh' }}>
                {/* Right Pane Header */}
                <div className="tree-header d-flex justify-content-between align-items-center p-2 border-bottom">
                  <span>Metadata Mapper</span>
                </div>
                <div className="flex-grow-1 p-4 d-flex flex-column">
                  {selectedNode ? (
                    <>
                      <div className="mb-4">
                        <h4 className="mb-3">{selectedNode.name}</h4>
                        <div className="d-flex flex-wrap gap-2 mb-3">
                          <Badge bg="secondary">Type: {selectedNode.type}</Badge>
                          {selectedNode.children && (
                            <Badge bg="light" text="dark">
                              {selectedNode.children.length} {selectedNode.children.length === 1 ? 'child' : 'children'}
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex-grow-1 d-flex flex-column">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <div className="d-flex align-items-center">
                            <h5 className="mb-0 me-2">Metadata</h5>
                            <Button
                              variant="outline-primary"
                              size="sm"
                              onClick={handleAddMetadata}
                              title="Add Metadata"
                              className="p-0 d-flex align-items-center justify-content-center me-2"
                              style={{ width: '24px', height: '24px' }}
                            >
                              +
                            </Button>
                            {hasUnsavedChanges && (
                              <Button
                                variant="success"
                                size="sm"
                                onClick={handleSaveMetadata}
                                title="Save Changes"
                              >
                                Save
                              </Button>
                            )}
                          </div>
                          {hasUnsavedChanges && (
                            <small className="text-warning">
                              You have unsaved changes
                            </small>
                          )}
                        </div>
                        <div className="flex-grow-1" style={{ minHeight: '300px' }}>
                          <DataGrid
                            key={`${selectedNode?.id}-${tempMetadata.length}`}
                            columns={metadataColumns}
                            rows={tempMetadata}
                            onRowsChange={(rows: any[]) => {
                              const updatedRows = [...rows as MetadataItem[]];
                              setTempMetadata(updatedRows);
                              setHasUnsavedChanges(true);
                            }}
                            rowHeight={40}
                            className="rdg-light"
                            style={{ height: '100%' }}
                            defaultColumnOptions={{
                              resizable: true,
                              sortable: true
                            }}
                          />
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="d-flex flex-column align-items-center justify-content-center h-100">
                      <p className="text-muted mb-4">Select a node to view and edit its metadata</p>
                      <div className="text-center text-muted">
                        <i className="bi bi-folder2-open" style={{ fontSize: '2rem', opacity: 0.5 }}></i>
                        <p className="mt-2">No node selected</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </main>

      {/* Footer */}
      <footer className="bg-dark text-white py-3 mt-auto">
        <Container className="text-center">
          <p className="mb-0">&copy; {new Date().getFullYear()} This Application. All rights reserved.</p>
        </Container>
      </footer>

      {/* Save Template Modal */}
      <Modal show={showSaveModal} onHide={() => setShowSaveModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {templateInfo?.id && !templateInfo.id.startsWith('template-') ? 'Update Template' : 'Save Template'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-4">
            <div className="alert alert-info">
              <h6 className="mb-2">Template Setup</h6>
              <p className="mb-0">Configure your template settings before saving.</p>
            </div>
          </div>

          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Template Name *</Form.Label>
              <Form.Control
                type="text"
                value={saveFormData.templateName}
                onChange={(e) => setSaveFormData(prev => ({ ...prev, templateName: e.target.value }))}
                placeholder="e.g., KitchenSync"
                required
              />
              <Form.Text className="text-muted">
                Name for your template (used for grouping)
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Version *</Form.Label>
              <Form.Control
                type="text"
                value={saveFormData.version}
                onChange={(e) => setSaveFormData(prev => ({ ...prev, version: e.target.value }))}
                placeholder="e.g., 1.0.0"
                required
              />
              <Form.Text className="text-muted">
                Semantic version for your template
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Stage *</Form.Label>
              <Form.Select
                value={saveFormData.stage}
                onChange={(e) => setSaveFormData(prev => ({ ...prev, stage: e.target.value as any }))}
                required
              >
                <option value="draft">Draft</option>
                <option value="beta">Beta</option>
                <option value="live">Live</option>
                <option value="archived">Archived</option>
              </Form.Select>
              <Form.Text className="text-muted">
                Current stage of your template
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={saveFormData.description}
                onChange={(e) => setSaveFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this template is for..."
              />
              <Form.Text className="text-muted">
                Optional description for your template
              </Form.Text>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="outline-secondary" onClick={() => setShowSaveModal(false)}>
            Cancel
          </Button>
          <Button
            variant="success"
            onClick={handleSaveTemplate}
            disabled={isSaving}
            className="d-flex align-items-center gap-2"
          >
            {isSaving ? (
              <>
                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                {templateInfo?.id && !templateInfo.id.startsWith('template-') ? 'Updating...' : 'Saving...'}
              </>
            ) : (
              <>
                💾
                {templateInfo?.id && !templateInfo.id.startsWith('template-') ? 'Update Template' : 'Save Template'}
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Toast Container for notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </div>
  );
}

export default App;
