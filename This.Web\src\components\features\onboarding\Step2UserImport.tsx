import React, { useState, useEffect } from "react";
import {
  CheckCircle,
  Loader,
  XCircle,
  User,
  Users,
} from "lucide-react";
import { useOnboarding } from "../../../contexts/OnboardingContext";
import { ApplicationDetails } from "../../../types/onboarding";

interface ImportedUser {
  id: string;
  userName: string;
  firstName: string;
  lastName: string;
  userId: string;
  email: string;
  isActive: boolean;
  emailConfirmed: boolean;
  phoneNumber: string;
  imageUrl: string | null;
  isDeleted: boolean;
  lastModifiedOn: string;
  createdOn: string;
  createdBy: string;
  lastModifiedBy: string;
  isMFAEnabled: boolean;
  otp: string | null;
  otpUpdatedOn: string | null;
  timeZoneInfo: string | null;
  licenseNo: string | null;
}

interface Step2Props {
  tenantId: string;
  applicationDetails: ApplicationDetails;
  onComplete: (users: ImportedUser[]) => void;
  onError?: (error: string) => void;
}

const Step2UserImport: React.FC<Step2Props> = ({
  tenantId,
  applicationDetails,
  onComplete,
  onError,
}) => {
  const [users, setUsers] = useState<ImportedUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasImported, setHasImported] = useState(false);
  const [noUsersFound, setNoUsersFound] = useState(false);
  const [loadingStage, setLoadingStage] = useState<'fetching' | 'creating' | 'loading' | 'completed' | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const { importUsers: importUsersFromContext } = useOnboarding();

  // Check if users are already imported from applicationDetails
  const isUserImported = React.useMemo(() => {
    return applicationDetails.isUserImported || false;
  }, [applicationDetails]);

  // Import users using the context with loading stage tracking
  const importUsersWithStages = async (tenantId: string): Promise<ImportedUser[]> => {
    try {
      setLoadingStage('fetching');

      // Use the context to import users (this handles both fetching and bulk creation)
      const importedUsers = await importUsersFromContext(tenantId, undefined, applicationDetails);

      if (importedUsers.length === 0) {
        return [];
      }

      setLoadingStage('loading');
      setSuccessMessage(`Successfully imported ${importedUsers.length} users from external system`);

      // Convert User[] to ImportedUser[] format for compatibility
      const convertedUsers: ImportedUser[] = importedUsers.map((user) => ({
        ...user,
        userId: user.id, // Map id to userId for compatibility
        otp: user.otp || null,
        otpUpdatedOn: user.otpUpdatedOn || null,
        timeZoneInfo: user.timeZoneInfo || null,
        licenseNo: user.licenseNo || null,
      }));

      return convertedUsers;
    } catch (error) {
      // Add context to the error before re-throwing
      if (error instanceof Error) {
        throw new Error(`User import failed: ${error.message}`);
      }
      throw new Error('User import failed due to an unknown error');
    }
  };

  // Auto-import users when component mounts if users aren't already imported
  useEffect(() => {
    if (tenantId && !hasImported && !loading && !error) {
      if (isUserImported) {
        // If users are already imported, mark as complete
        setHasImported(true);
        onComplete([]);
      } else {
        importUsers();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tenantId, hasImported, isUserImported]); // Removed loading and error from dependencies to prevent loops

  const importUsers = async () => {
    // Prevent multiple submissions
    if (loading) return;

    setLoading(true);
    setError(null);
    setNoUsersFound(false);
    setSuccessMessage(null);

    try {
      // Skip if users are already imported
      if (isUserImported) {
        setHasImported(true);
        onComplete([]);
        return;
      }

      // Use the simplified import function
      const importedUsers = await importUsersWithStages(tenantId);

      if (importedUsers.length === 0) {
        setNoUsersFound(true);
        setLoadingStage('completed');
        setHasImported(true);
        return; // Don't proceed if no users found
      }

      setUsers(importedUsers);
      setHasImported(true);
      setLoadingStage('completed');

      // Auto proceed to next step after showing success message
      setTimeout(() => {
        onComplete(importedUsers);
      }, 2000);

    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : "Failed to import users";
      setError(errorMsg);
      onError?.(errorMsg);
      setNoUsersFound(false);
      setHasImported(true); // Mark as attempted to prevent useEffect from retrying
    } finally {
      setLoading(false);
      setLoadingStage(null);
    }
  };

  const renderUserList = () => {
    if (users.length === 0) return null;

    const activeUsers = users.filter((user) => user.isActive).length;
    const verifiedEmails = users.filter((user) => user.emailConfirmed).length;

    return (
      <div className="space-y-3">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <h3 className="text-sm font-medium text-gray-900">Imported Users</h3>
          <div className="flex flex-wrap gap-2">
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xxs font-medium bg-green-100 text-green-800">
              {users.length} Total
            </span>
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xxs font-medium bg-blue-100 text-blue-800">
              {activeUsers} Active
            </span>
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xxs font-medium bg-purple-100 text-purple-800">
              {verifiedEmails} Verified Emails
            </span>
          </div>
        </div>

        <div className="bg-white shadow overflow-hidden rounded-lg border border-gray-200">
          <ul className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
            {users.map((user, index) => (
              <li
                key={user.id || `user-${index}`}
                className="px-4 py-3 sm:px-6 hover:bg-gray-50 transition-colors duration-150"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center min-w-0">
                    <div className="flex-shrink-0 h-9 w-9 flex items-center justify-center rounded-full bg-primary-50">
                      <User className="h-4 w-4 text-primary-600" />
                    </div>
                    <div className="ml-3 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-xs font-medium text-gray-900 truncate">
                          {user.firstName} {user.lastName}
                        </p>
                        {user.isActive && (
                          <span className="flex-shrink-0 inline-flex items-center px-2 py-0.5 rounded text-xxs font-medium bg-green-100 text-green-800">
                            Active
                          </span>
                        )}
                      </div>
                      <p className="text-xxs text-gray-500 truncate mt-0.5">
                        {user.email}
                      </p>
                    </div>
                  </div>
                  <div className="ml-2 flex-shrink-0">
                    {user.emailConfirmed ? (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xxs font-medium bg-green-50 text-green-800 border border-green-100">
                        Verified
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xxs font-medium bg-yellow-50 text-yellow-800 border border-yellow-100">
                        Pending
                      </span>
                    )}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-6">
      <div className="text-center mb-6">
        <div className="mx-auto flex items-center justify-center h-10 w-10 rounded-full bg-primary-50 mb-3">
          <Users className="h-5 w-5 text-primary-600" />
        </div>
        <h2 className="text-sm font-bold text-gray-900 mb-1.5">Import Users</h2>
        <p className="text-gray-600 text-xs">
          Importing users from Leadrat for tenant:{" "}
          <span className="font-medium text-gray-800">{tenantId}</span>
        </p>
      </div>

      {loading && (
        <div className="text-center py-8">
          <div className="inline-flex items-center px-4 py-2 bg-primary-50 rounded-full">
            <Loader className="w-4 h-4 text-primary-500 mr-2 animate-spin" />
            <span className="text-gray-700 font-medium text-xs">
              {loadingStage === 'fetching' && 'Fetching users from external system...'}
              {loadingStage === 'creating' && 'Creating users in the system...'}
              {loadingStage === 'loading' && 'Loading users...'}
              {loadingStage === 'completed' && 'Processing completed users...'}
              {!loadingStage && 'Importing users from Leadrat database...'}
            </span>
          </div>
        </div>
      )}

      {/* Success message display */}
      {successMessage && !loading && (
        <div className="mb-6 p-3 bg-green-50 border border-green-100 rounded-lg">
          <div className="flex items-center justify-center space-x-2 text-green-700">
            <CheckCircle className="w-4 h-4 flex-shrink-0" />
            <span className="text-xs font-medium">{successMessage}</span>
          </div>
        </div>
      )}

      {hasImported && !error && !noUsersFound && (
        <div className="mb-6 p-3 bg-green-50 border border-green-100 rounded-lg">
          <div className="flex items-center justify-center space-x-2 text-green-700">
            <CheckCircle className="w-4 h-4 flex-shrink-0" />
            <span className="text-xs font-medium">
              Successfully imported {users.length} users! Proceeding to role
              assignment...
            </span>
          </div>
        </div>
      )}

      {hasImported && !error && !loading && noUsersFound && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-100 rounded-lg">
          <div className="flex flex-col items-center space-y-3 text-blue-700">
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 flex-shrink-0" />
              <div className="text-xs font-medium">No Users Found</div>
            </div>
            <div className="text-xxs font-medium">
              No users are available for this tenant.
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="mb-6 p-4 bg-white border border-red-100 rounded-lg shadow-xs">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircle className="h-4 w-4 text-red-500 mt-0.5" />
            </div>
            <div className="ml-3">
              <h3 className="text-xs font-medium text-red-800">
                {error.includes('external system') || error.includes('External user system') || error.includes('Cannot connect to external') ? 'External System Error' :
                 error.includes('User creation failed') || error.includes('bulk-create') || error.includes('create users in system') ? 'User Creation Error' :
                 error.includes('Failed to load users') || error.includes('Failed to fetch users') ? 'User Loading Error' :
                 'Import Failed'}
              </h3>
              <div className="mt-1 text-xs text-red-700">
                <p>{error}</p>
                {(error.includes('external system') || error.includes('External user system')) && (
                  <p className="mt-1 text-xs text-red-600">
                    This error occurred while fetching users from the external system. Please check your connection and try again.
                  </p>
                )}
                {(error.includes('User creation failed') || error.includes('bulk-create')) && (
                  <p className="mt-1 text-xs text-red-600">
                    This error occurred while creating users in the system. Please check the server logs for more details.
                  </p>
                )}
                {error.includes('Failed to load users') && (
                  <p className="mt-1 text-xs text-red-600">
                    This error occurred while loading users from the system. Please try again.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-6">{renderUserList()}</div>
    </div>
  );
};

export default Step2UserImport;
