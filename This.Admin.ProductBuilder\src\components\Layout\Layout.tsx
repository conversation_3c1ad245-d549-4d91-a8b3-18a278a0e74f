import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FileText, CreditCard, Settings, Menu, X, Database } from 'lucide-react';
import { TenantSelector } from '../TenantSelector';

interface LayoutProps {
  children: React.ReactNode;
}

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
}

const sidebarItems: SidebarItem[] = [
  {
    id: 'templates',
    label: 'Templates',
    icon: FileText,
    path: '/templates'
  },
  {
    id: 'subscriptions',
    label: 'Subscriptions',
    icon: CreditCard,
    path: '/subscriptions'
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    path: '/settings'
  },
  {
    id: 'tenant-api-test',
    label: 'API Test',
    icon: Database,
    path: '/tenant-api-test'
  }
];

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div style={{ display: 'flex', height: '100vh', backgroundColor: '#f8f9fa', overflow: 'hidden' }}>
      {/* Sidebar - Fixed */}
      <div
        style={{
          width: isCollapsed ? '64px' : '176px',
          backgroundColor: 'white',
          borderRight: '1px solid #e9ecef',
          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
          display: 'flex',
          flexDirection: 'column',
          transition: 'width 0.3s ease-in-out',
          position: 'fixed',
          left: 0,
          top: 0,
          height: '100vh',
          zIndex: 1000,
          overflow: 'hidden'
        }}
      >
        {/* Header - Card Style */}
        <div
          style={{
            margin: isCollapsed ? '8px 4px' : '12px 8px',
            padding: isCollapsed ? '8px' : '12px',
            backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.25)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            minHeight: '48px',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Subtle pattern overlay */}
          <div
            style={{
              position: 'absolute',
              inset: 0,
              background: 'radial-gradient(circle at 50% 50%, rgba(255,255,255,0.1) 0%, transparent 70%)',
              pointerEvents: 'none'
            }}
          />
          
          {!isCollapsed && (
            <div style={{ 
              display: 'flex', 
              flexDirection: 'column', 
              minWidth: 0,
              position: 'relative',
              zIndex: 1
            }}>
              <h1 style={{
                fontSize: '14px',
                fontWeight: '700',
                color: 'white',
                margin: 0,
                lineHeight: '1.2',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                textShadow: '0 1px 2px rgba(0,0,0,0.2)'
              }}>
                Admin Panel
              </h1>
              <p style={{
                fontSize: '11px',
                color: 'rgba(255, 255, 255, 0.8)',
                margin: 0,
                lineHeight: '1.2',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                fontWeight: '500'
              }}>
                Management Console
              </p>
            </div>
          )}
          
          <button
            onClick={toggleSidebar}
            style={{
              padding: '8px',
              borderRadius: '8px',
              border: 'none',
              backgroundColor: 'rgba(255, 255, 255, 0.15)',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.2s ease',
              color: 'white',
              flexShrink: 0,
              backdropFilter: 'blur(10px)',
              position: 'relative',
              zIndex: 1
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.25)';
              e.currentTarget.style.transform = 'scale(1.05)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.15)';
              e.currentTarget.style.transform = 'scale(1)';
            }}
            aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {isCollapsed ? (
              <Menu size={16} />
            ) : (
              <X size={16} />
            )}
          </button>
        </div>

        {/* Tenant Selector */}
        <div style={{
          margin: isCollapsed ? '8px 4px' : '8px 8px',
          padding: isCollapsed ? '8px 4px' : '8px 8px'
        }}>
          <TenantSelector isCollapsed={isCollapsed} />
        </div>

        {/* Navigation */}
        <nav style={{
          flex: 1,
          padding: '8px 0',
          overflowY: 'auto',
          overflowX: 'hidden'
        }}>
          <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
            {sidebarItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.path);
              
              return (
                <li key={item.id} style={{ marginBottom: '2px' }}>
                  <Link
                    to={item.path}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: isCollapsed ? '10px 8px' : '10px 12px',
                      margin: isCollapsed ? '0 8px' : '0 8px',
                      borderRadius: '6px',
                      textDecoration: 'none',
                      fontSize: '13px',
                      fontWeight: '500',
                      transition: 'all 0.2s ease',
                      color: active ? '#0d6efd' : '#6c757d',
                      backgroundColor: active ? '#e7f3ff' : 'transparent',
                      borderLeft: active && !isCollapsed ? '2px solid #0d6efd' : '2px solid transparent',
                      justifyContent: isCollapsed ? 'center' : 'flex-start'
                    }}
                    onMouseEnter={(e) => {
                      if (!active) {
                        e.currentTarget.style.backgroundColor = '#f8f9fa';
                        e.currentTarget.style.color = '#212529';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!active) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = '#6c757d';
                      }
                    }}
                    title={isCollapsed ? item.label : undefined}
                  >
                    <Icon
                      size={16}
                      style={{
                        flexShrink: 0,
                        marginRight: isCollapsed ? 0 : '8px'
                      }}
                    />
                    {!isCollapsed && (
                      <span style={{ 
                        whiteSpace: 'nowrap', 
                        overflow: 'hidden', 
                        textOverflow: 'ellipsis',
                        fontSize: '12px',
                        fontWeight: '500'
                      }}>
                        {item.label}
                      </span>
                    )}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Footer - Version info */}
        <div
          style={{
            padding: isCollapsed ? '8px' : '8px 12px',
            borderTop: '1px solid #e9ecef',
            backgroundColor: '#f8f9fa'
          }}
        >
          {isCollapsed ? (
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center',
              alignItems: 'center'
            }}>
              <div style={{ 
                width: '6px', 
                height: '6px', 
                backgroundColor: '#6c757d', 
                borderRadius: '50%'
              }} title="Version 1.0.0"></div>
            </div>
          ) : (
            <div style={{ fontSize: '11px', color: '#6c757d', lineHeight: '1.2' }}>
              <div style={{ fontWeight: '500' }}>v1.0.0</div>
              <div>&copy; 2024</div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content - Scrollable */}
      <div style={{
        marginLeft: isCollapsed ? '64px' : '176px',
        width: `calc(100vw - ${isCollapsed ? '64px' : '176px'})`,
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        <main style={{
          flex: 1,
          overflowY: 'auto',
          overflowX: 'hidden',
          backgroundColor: '#f8f9fa',
          padding: 0
        }}>
          {children}
        </main>
      </div>
    </div>
  );
};