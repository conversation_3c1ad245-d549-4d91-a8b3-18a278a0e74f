import React from "react";
import { AlertTriangle } from "lucide-react";

interface TenantNotFoundErrorProps {
  tenantId?: string;
}

const TenantNotFoundError: React.FC<TenantNotFoundErrorProps> = ({
  tenantId,
}) => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-6" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Tenant Not Found
          </h1>
          <p className="text-gray-600 mb-6">Tenant not found or inactive.</p>
          {tenantId && (
            <div className="bg-gray-50 rounded-md p-4 mb-6">
              <p className="text-sm text-gray-700">
                <span className="font-medium">Tenant ID:</span> {tenantId}
              </p>
            </div>
          )}
          <p className="text-sm text-gray-500">
            Please contact your administrator for assistance.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TenantNotFoundError;
