import { Check, Loader, UserPlus, XCircle, Users } from "lucide-react";
import React, { useEffect, useState } from "react";
import Select, { components, MultiValue, ActionMeta } from "react-select";
import { useOnboarding } from "../../../contexts/OnboardingContext";
import { ApplicationDetails, User, Role } from "../../../types/onboarding";

interface UserOption {
  value: string;
  label: string;
  email: string;
  isSelectAll?: boolean;
}

interface Step3RoleAssignmentProps {
  tenantId: string;
  users: User[];
  applicationDetails: ApplicationDetails;
  onComplete: (data: Record<string, unknown>) => void;
  onError?: (error: string) => void;
}

// Custom Option component for Select All functionality
const CustomOption = (props: any) => {
  const { data, innerRef, innerProps, selectProps } = props;

  if (data.isSelectAll) {
    // Check if all regular users are selected
    const allUserOptions = selectProps.options.filter((opt: UserOption) => !opt.isSelectAll);
    const selectedValues = selectProps.value || [];
    const allUsersSelected = allUserOptions.length > 0 &&
      allUserOptions.every((opt: UserOption) =>
        selectedValues.some((selected: UserOption) => selected.value === opt.value)
      );

    return (
      <div
        ref={innerRef}
        {...innerProps}
        className="px-3 py-2 cursor-pointer border-b border-slate-300 bg-slate-100 hover:bg-slate-200 transition-colors duration-150"
      >
        <div className="flex items-center space-x-2">
          <div className={`flex items-center justify-center w-4 h-4 border-2 rounded ${
            allUsersSelected
              ? 'bg-slate-600 border-slate-600'
              : 'border-slate-400'
          }`}>
            {allUsersSelected && <Check className="w-3 h-3 text-white" />}
          </div>
          <span className="font-medium text-slate-700 text-xs">
            {allUsersSelected ? 'Deselect All Users' : 'Select All Users'}
          </span>
          <span className="text-slate-500 text-xs">
            ({allUserOptions.length} users)
          </span>
        </div>
      </div>
    );
  }

  return (
    <components.Option {...props}>
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium text-xs text-slate-700">{data.label}</div>
          <div className="text-slate-500 text-xs">{data.email}</div>
        </div>
      </div>
    </components.Option>
  );
};

const Step3RoleAssignment: React.FC<Step3RoleAssignmentProps> = (props) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [success, setSuccess] = useState<string>("");
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [roleUserSelections, setRoleUserSelections] = useState<{
    [roleId: string]: string[];
  }>({});
  const [expandedRoles, setExpandedRoles] = useState<{
    [roleId: string]: boolean;
  }>({});

  const { tenantId, onComplete } = props;
  const { fetchUsersAndRoles, fetchRoles, assignRoles } = useOnboarding();

  // Check if any role has users assigned
  const hasAnyAssignments = () => {
    return Object.values(roleUserSelections).some((users) => users.length > 0);
  };

  // Helper function to create unique user labels
  const createUniqueUserLabels = (userList: User[]): Map<string, string> => {
    const labelCounts = new Map<string, number>();
    const userLabels = new Map<string, string>();

    userList.forEach(user => {
      // Ensure user.id exists before proceeding
      if (!user.id) {
        console.warn('User without ID found:', user);
        return;
      }

      const baseName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
      const baseLabel = baseName || user.email || user.id;

      // Track label occurrences to handle duplicates
      const currentCount = labelCounts.get(baseLabel) || 0;
      labelCounts.set(baseLabel, currentCount + 1);

      // Make label unique by appending user ID if there are duplicates
      const uniqueLabel = currentCount > 0 ? `${baseLabel} (${user.id.slice(-8)})` : baseLabel;
      userLabels.set(user.id, uniqueLabel);
    });

    return userLabels;
  };

  // Helper function to create user options with Select All option
  const createUserOptions = (roleId: string): UserOption[] => {
    // Sort users alphabetically by full name
    const sortedUsers = [...users].sort((a, b) => {
      const nameA = `${a.firstName || ''} ${a.lastName || ''}`.trim().toLowerCase() || a.email?.toLowerCase() || a.id;
      const nameB = `${b.firstName || ''} ${b.lastName || ''}`.trim().toLowerCase() || b.email?.toLowerCase() || b.id;
      return nameA.localeCompare(nameB);
    });

    // Create unique labels for all users
    const userLabels = createUniqueUserLabels(sortedUsers);
    const userOptions: UserOption[] = sortedUsers.map((user) => ({
      value: user.id,
      label: userLabels.get(user.id) || user.id,
      email: user.email,
    }));

    const selectedUserIds = roleUserSelections[roleId] || [];
    const allUsersSelected = selectedUserIds.length === users.length && users.length > 0;

    const selectAllOption: UserOption = {
      value: 'select-all',
      label: allUsersSelected ? 'Deselect All Users' : 'Select All Users',
      email: '',
      isSelectAll: true,
    };

    return [selectAllOption, ...userOptions];
  };

  // Helper function to get sorted selected users for display
  const getSortedSelectedUsers = (roleId: string) => {
    const selectedUserIds = roleUserSelections[roleId] || [];
    return selectedUserIds
      .map((userId) => users.find(u => u.id === userId))
      .filter((user): user is User => user !== undefined)
      .sort((a, b) => {
        const nameA = `${a.firstName || ''} ${a.lastName || ''}`.trim().toLowerCase() || a.email?.toLowerCase() || a.id;
        const nameB = `${b.firstName || ''} ${b.lastName || ''}`.trim().toLowerCase() || b.email?.toLowerCase() || b.id;
        return nameA.localeCompare(nameB);
      });
  };

  // Handle Select All functionality
  const handleSelectChange = (roleId: string, selectedOptions: MultiValue<UserOption>, actionMeta: ActionMeta<UserOption>) => {
    const regularOptions = selectedOptions.filter(option => !option.isSelectAll);

    if (actionMeta.action === 'select-option' && actionMeta.option?.isSelectAll) {
      // Select All was clicked
      const currentSelections = roleUserSelections[roleId] || [];
      const allUserIds = users.map(user => user.id);

      if (currentSelections.length === users.length) {
        // All users are selected, so deselect all
        setRoleUserSelections((prev) => ({
          ...prev,
          [roleId]: [],
        }));
      } else {
        // Not all users are selected, so select all
        setRoleUserSelections((prev) => ({
          ...prev,
          [roleId]: allUserIds,
        }));
      }
    } else if (actionMeta.action === 'deselect-option' && actionMeta.option?.isSelectAll) {
      // Deselect All was clicked (shouldn't happen with current logic, but for safety)
      setRoleUserSelections((prev) => ({
        ...prev,
        [roleId]: [],
      }));
    } else {
      // Regular option selection/deselection
      const selectedUserIds = regularOptions.map(option => option.value);
      setRoleUserSelections((prev) => ({
        ...prev,
        [roleId]: selectedUserIds,
      }));
    }
  };

  // Handle bulk role assignment
  const handleAssignRoles = async () => {
    // Prevent multiple submissions
    if (submitting) return;

    if (!hasAnyAssignments()) {
      setError("Please assign at least one user to any role");
      return;
    }

    setSubmitting(true);
    setError("");
    setSuccess("");

    try {
      // Prepare the role assignments from the new structure
      const roleAssignments = Object.entries(roleUserSelections)
        .filter(([, userIds]) => userIds.length > 0)
        .flatMap(([roleId, userIds]) =>
          userIds.map((userId) => ({
            userId,
            roleId,
          }))
        );

      const productId = props.applicationDetails.id;
      await assignRoles(
        tenantId,
        roleAssignments,
        productId
      );

      // Reset selections
      setRoleUserSelections({});

      // Show success message
      setSuccess("Roles assigned successfully!");
      setTimeout(() => setSuccess(""), 3000);

      // Move to next step after a short delay
      setTimeout(() => onComplete({ success: true }), 3000);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to assign roles. Please try again.";
      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Use users passed from Step2 to avoid duplicate getUsers API call
        if (props.users && props.users.length > 0) {
          setUsers(props.users);

          // Only fetch roles since we already have users
          const rolesData = await fetchRoles(tenantId, true);
          setRoles(rolesData);
        } else {
          // Fallback: fetch both users and roles if no users passed from Step2
          const { users: usersData, roles: rolesData } = await fetchUsersAndRoles(
            tenantId,
            true
          );
          setRoles(rolesData);
          setUsers(usersData);
        }
      } catch {
        setError("Failed to load data. Please refresh the page.");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [tenantId, props.users, fetchUsersAndRoles, fetchRoles]);

  return (
    <div className="max-w-6xl mx-auto space-y-4">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-xl font-bold text-gray-900 mb-1 ml-2">
          Assign User Roles
        </h2>
        <p className="text-xs text-gray-600 max-w-xl mx-auto">
          Efficiently assign multiple users to roles and manage permissions
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 rounded-lg p-2">
          <div className="flex items-center">
            <XCircle className="h-3 w-3 text-red-400" />
            <div className="ml-2 text-xs text-red-700">{error}</div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 rounded-lg p-2">
          <div className="flex items-center">
            <Check className="h-3 w-3 text-green-400" />
            <div className="ml-2 text-xs text-green-700">{success}</div>
          </div>
        </div>
      )}

      {/* Main Content - Two Column Layout */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Left Column - Roles */}
        <div className="xl:col-span-2">
          <div className="bg-white rounded-lg shadow border border-gray-100 overflow-visible">
            <div className="bg-slate-600 rounded-t-lg px-3 py-2">
              <div className="flex items-center space-x-2">
                <div className="flex-shrink-0">
                  <div className="w-6 h-6 bg-slate-500 rounded-lg flex items-center justify-center">
                    <Users className="w-3 h-3 text-slate-100" />
                  </div>
                </div>
                <div>
                  <h3 className="text-base font-semibold text-white">
                    Available Roles
                  </h3>
                  <p className="text-slate-200 text-xs">
                    Select roles and assign users to them
                  </p>
                </div>
              </div>
            </div>

            <div className="p-4 pb-8 overflow-visible">
              {loading ? (
                <div className="text-center py-6">
                  <div className="inline-flex items-center justify-center w-10 h-10 bg-primary-50 rounded-full mb-2">
                    <Loader className="animate-spin h-5 w-5 text-primary-500" />
                  </div>
                  <p className="text-gray-600 font-medium text-xs">
                    Loading roles...
                  </p>
                </div>
              ) : roles.length > 0 ? (
                <div className="space-y-3">
                  {[...roles].sort((a, b) => a.name.localeCompare(b.name)).map((role, index) => (
                    <div key={role.id} className="group relative">
                      <div className="bg-white rounded-lg p-3 border border-slate-200 hover:border-slate-300 hover:shadow-md transition-all duration-200">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <div className="flex-shrink-0">
                                <div className="w-5 h-5 bg-slate-600 rounded-lg flex items-center justify-center">
                                  <span className="text-white font-semibold text-xs">
                                    {index + 1}
                                  </span>
                                </div>
                              </div>
                              <h4 className="text-sm font-semibold text-slate-800">
                                {role.name}
                              </h4>
                            </div>
                            {role.description && (
                              <p className="text-slate-600 text-xs leading-relaxed ml-7">
                                {role.description}
                              </p>
                            )}
                          </div>
                          <div className={`flex items-center space-x-1 rounded-lg px-2 py-1 border transition-colors duration-200 ${
                            roleUserSelections[role.id]?.length > 0
                              ? 'bg-slate-600 border-slate-600'
                              : 'bg-slate-50 border-slate-200'
                          }`}>
                            <Users className={`h-3 w-3 ${
                              roleUserSelections[role.id]?.length > 0
                                ? 'text-white'
                                : 'text-slate-600'
                            }`} />
                            <span className={`text-xs font-medium ${
                              roleUserSelections[role.id]?.length > 0
                                ? 'text-white'
                                : 'text-slate-700'
                            }`}>
                              {roleUserSelections[role.id]?.length || 0}
                            </span>
                            {roleUserSelections[role.id]?.length > 0 && (
                              <span className="text-xs text-slate-200">
                                assigned
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Assign Users to {role.name}
                            </label>
                            <div className="relative">
                              <Select<UserOption, true>
                                isMulti
                                instanceId={`role-select-${role.id}`}
                                key={`role-select-${role.id}`}
                                value={(() => {
                                  const sortedSelectedUsers = getSortedSelectedUsers(role.id);
                                  // Create consistent labels that match the options
                                  const userLabels = createUniqueUserLabels(sortedSelectedUsers);
                                  return sortedSelectedUsers.map(user => ({
                                    value: user.id,
                                    label: userLabels.get(user.id) || user.id,
                                    email: user.email,
                                  }));
                                })()}
                                onChange={(selectedOptions, actionMeta) => {
                                  handleSelectChange(role.id, selectedOptions, actionMeta);
                                }}
                                options={createUserOptions(role.id)}
                                getOptionValue={(option) => option.value}
                                getOptionLabel={(option) => option.label}
                                components={{ Option: CustomOption }}
                                placeholder={`🔍 Search and select users... (${users.length} available)`}
                                className="text-xs"
                                classNamePrefix="react-select"
                                isDisabled={loading || submitting}
                                noOptionsMessage={() => "No users available"}
                                hideSelectedOptions={true}
                                closeMenuOnSelect={false}
                                styles={{
                                  control: (base, state) => ({
                                    ...base,
                                    borderColor: state.isFocused
                                      ? "#6B7280"
                                      : "#D1D5DB",
                                    boxShadow: state.isFocused
                                      ? "0 0 0 2px rgba(107, 114, 128, 0.1)"
                                      : "none",
                                    borderRadius: "0.375rem",
                                    padding: "0.125rem",
                                    minHeight: "2rem",
                                    fontSize: "0.75rem",
                                  }),
                                  menu: (base) => ({
                                    ...base,
                                    zIndex: 9999,
                                    position: "absolute",
                                    fontSize: "0.75rem",
                                    backgroundColor: "#F8FAFC", // Light slate background
                                    border: "1px solid #E2E8F0", // Slate border
                                    boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                                  }),
                                  menuList: (base) => ({
                                    ...base,
                                    backgroundColor: "#F8FAFC", // Light slate background
                                    padding: "0",
                                  }),
                                  menuPortal: (base) => ({
                                    ...base,
                                    zIndex: 9999,
                                  }),
                                  option: (base, state) => ({
                                    ...base,
                                    backgroundColor: state.isFocused
                                      ? "#E2E8F0" // Slate-200 on hover
                                      : state.isSelected
                                        ? "#CBD5E1" // Slate-300 when selected
                                        : "#F8FAFC", // Light slate background
                                    color: "#334155", // Slate-700 text
                                    fontSize: "0.75rem",
                                    padding: "8px 12px",
                                    cursor: "pointer",
                                    ":active": {
                                      backgroundColor: "#CBD5E1", // Slate-300
                                    },
                                  }),
                                  multiValue: (base) => ({
                                    ...base,
                                    backgroundColor: "#E2E8F0", // Slate-200
                                    borderRadius: "0.25rem",
                                    fontSize: "0.625rem",
                                    border: "1px solid #CBD5E1", // Slate-300 border
                                  }),
                                  multiValueLabel: (base) => ({
                                    ...base,
                                    color: "#475569", // Slate-600
                                    fontWeight: "500",
                                    fontSize: "0.625rem",
                                  }),
                                  multiValueRemove: (base) => ({
                                    ...base,
                                    color: "#64748B", // Slate-500
                                    ":hover": {
                                      backgroundColor: "#CBD5E1", // Slate-300
                                      color: "#334155", // Slate-700
                                    },
                                  }),
                                }}
                                menuPortalTarget={document.body}
                                menuPosition="fixed"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <div className="inline-flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full mb-2">
                    <Users className="w-5 h-5 text-gray-400" />
                  </div>
                  <p className="text-gray-600 font-medium text-xs">
                    No roles available
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Column - Summary */}
        <div className="xl:col-span-1">
          <div className="bg-white rounded-lg shadow border border-gray-100 overflow-hidden sticky top-4 max-h-[calc(100vh-2rem)]">
            <div className="bg-slate-600 px-3 py-2">
              <div className="flex items-center space-x-2">
                <div className="flex-shrink-0">
                  <div className="w-6 h-6 bg-slate-500 rounded-lg flex items-center justify-center">
                    <Check className="w-3 h-3 text-slate-100" />
                  </div>
                </div>
                <div>
                  <h3 className="text-base font-semibold text-white">
                    Assignment Summary
                  </h3>
                  <p className="text-slate-200 text-xs">
                    {hasAnyAssignments()
                      ? `${Object.values(roleUserSelections).reduce((total, users) => total + users.length, 0)} total assignments`
                      : 'No assignments yet'
                    }
                  </p>
                </div>
              </div>
            </div>

            <div className="p-3 overflow-y-auto max-h-[calc(100vh-8rem)]">
              {hasAnyAssignments() ? (
                <div className="space-y-3">
                  <div className="bg-slate-100 rounded-lg p-3 border border-slate-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-6 h-6 bg-slate-600 rounded-full flex items-center justify-center">
                          <Users className="w-3 h-3 text-white" />
                        </div>
                        <div>
                          <span className="text-sm font-medium text-slate-700">
                            Total Assignments
                          </span>
                          <div className="text-xs text-slate-500">
                            {Object.keys(roleUserSelections).filter(roleId => roleUserSelections[roleId].length > 0).length} roles assigned
                          </div>
                        </div>
                      </div>
                      <span className="text-xl font-bold text-slate-800">
                        {Object.values(roleUserSelections).reduce(
                          (total, users) => total + users.length,
                          0
                        )}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {Object.entries(roleUserSelections)
                      .filter(([, userIds]) => userIds.length > 0)
                      .sort(([roleIdA], [roleIdB]) => {
                        const roleA = roles.find((r) => r.id === roleIdA);
                        const roleB = roles.find((r) => r.id === roleIdB);
                        return (roleA?.name || '').localeCompare(roleB?.name || '');
                      })
                      .map(([roleId, userIds]) => {
                        const role = roles.find((r) => r.id === roleId);
                        // Sort user IDs by user names
                        const sortedUserIds = [...userIds].sort((a, b) => {
                          const userA = users.find((u) => u.id === a);
                          const userB = users.find((u) => u.id === b);
                          const nameA = userA ? `${userA.firstName || ''} ${userA.lastName || ''}`.trim().toLowerCase() || userA.email?.toLowerCase() || userA.id : '';
                          const nameB = userB ? `${userB.firstName || ''} ${userB.lastName || ''}`.trim().toLowerCase() || userB.email?.toLowerCase() || userB.id : '';
                          return nameA.localeCompare(nameB);
                        });
                        return role ? (
                          <div
                            key={roleId}
                            className="bg-white rounded-lg p-2 border border-slate-200"
                          >
                            <div className="flex items-center justify-between mb-1">
                              <div className="flex items-center space-x-1">
                                <div className="w-4 h-4 bg-slate-600 rounded-full flex items-center justify-center">
                                  <span className="text-white font-semibold text-xs">
                                    {sortedUserIds.length}
                                  </span>
                                </div>
                                <h4 className="text-xs font-semibold text-slate-800 truncate">
                                  {role.name}
                                </h4>
                              </div>
                            </div>
                            <div className="space-y-1">
                              {sortedUserIds.length <= 6 ? (
                                // Show all users if 6 or fewer - single column
                                sortedUserIds.map((userId, index) => {
                                  const user = users.find((u) => u.id === userId);
                                  return user ? (
                                    <div
                                      key={`${roleId}-summary-${userId}-${index}`}
                                      className="flex items-center text-xs"
                                    >
                                      <span className="text-gray-700 truncate flex-1">
                                        • {`${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email || user.id}
                                      </span>
                                    </div>
                                  ) : null;
                                })
                              ) : sortedUserIds.length <= 12 ? (
                                // 7-12 users: show in 2 columns
                                <div className="grid grid-cols-2 gap-x-2 gap-y-0.5">
                                  {sortedUserIds.map((userId, index) => {
                                    const user = users.find((u) => u.id === userId);
                                    return user ? (
                                      <div
                                        key={`${roleId}-summary-${userId}-${index}`}
                                        className="text-xs text-gray-700 truncate"
                                        title={`${`${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email || user.id} (${user.email})`}
                                      >
                                        • {`${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email || user.id}
                                      </div>
                                    ) : null;
                                  })}
                                </div>
                              ) : (
                                // More than 12 users: show first 10 in 2 columns + expandable
                                <>
                                  <div className="grid grid-cols-2 gap-x-2 gap-y-0.5">
                                    {sortedUserIds.slice(0, expandedRoles[roleId] ? sortedUserIds.length : 10).map((userId, index) => {
                                      const user = users.find((u) => u.id === userId);
                                      return user ? (
                                        <div
                                          key={`${roleId}-summary-${userId}-${index}`}
                                          className="text-xs text-gray-700 truncate"
                                          title={`${`${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email || user.id} (${user.email})`}
                                        >
                                          • {`${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email || user.id}
                                        </div>
                                      ) : null;
                                    })}
                                  </div>
                                  {!expandedRoles[roleId] && sortedUserIds.length > 10 && (
                                    <div className="mt-2 pt-1 border-t border-gray-200">
                                      <button
                                        type="button"
                                        className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                                        onClick={() => setExpandedRoles(prev => ({ ...prev, [roleId]: true }))}
                                      >
                                        View all {sortedUserIds.length} users
                                      </button>
                                    </div>
                                  )}
                                  {expandedRoles[roleId] && sortedUserIds.length > 10 && (
                                    <div className="mt-2 pt-1 border-t border-gray-200">
                                      <button
                                        type="button"
                                        className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                                        onClick={() => setExpandedRoles(prev => ({ ...prev, [roleId]: false }))}
                                      >
                                        Show less
                                      </button>
                                    </div>
                                  )}
                                </>
                              )}
                            </div>
                          </div>
                        ) : null;
                      })}
                  </div>

                  {/* Submit Button */}
                  <div className="pt-3 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={handleAssignRoles}
                      disabled={submitting || loading || !hasAnyAssignments()}
                      className={`w-full flex justify-center items-center py-2 px-3 border border-transparent rounded-lg shadow text-xs text-white transition-all duration-200 ${submitting || loading || !hasAnyAssignments()
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500"
                        }`}
                    >
                      {submitting ? (
                        <>
                          <Loader className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" />
                          <span>Assigning...</span>
                        </>
                      ) : (
                        <>
                          <UserPlus className="-ml-1 mr-1 h-3 w-3" />
                          <span>
                            Assign (
                            {Object.values(roleUserSelections).reduce(
                              (total, users) => total + users.length,
                              0
                            )}
                            )
                          </span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-6">
                  <div className="inline-flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full mb-3">
                    <Users className="w-5 h-5 text-gray-400" />
                  </div>
                  <h4 className="text-xs font-semibold text-gray-700 mb-1">
                    Ready to Assign
                  </h4>
                  <p className="text-gray-500 text-xs max-w-xs mx-auto">
                    Use the dropdowns on the left to assign users to roles. Your selections will appear here.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step3RoleAssignment;
