import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { AgGridReact } from 'ag-grid-react';
import type { ColDef, GridApi, GridReadyEvent } from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { RefreshCw, Users, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/shared/components/atoms/Button/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/molecules/Card/Card';
import { cn } from '@/shared/utils/utils';
import { ApiService, type User } from '../../services/apiService';

interface UsersPageProps {
  className?: string;
}

export const UsersPage: React.FC<UsersPageProps> = ({ className }) => {
  // State management
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);

  // Get tenant ID from localStorage (following existing pattern)
  const getCurrentTenant = (): string => {
    return localStorage.getItem('selectedTenantId') || 'kitchsync'; // fallback to default
  };

  // API service instance
  const apiService = ApiService.getInstance();

  // Fetch users data
  const fetchUsers = useCallback(async (useCache: boolean = true) => {
    try {
      setLoading(true);
      setError(null);
      
      const tenantId = getCurrentTenant();
      const usersData = await apiService.getUsers(tenantId, useCache);
      
      setUsers(usersData);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  }, [apiService]);

  // Initial data load
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    fetchUsers(false); // Force refresh without cache
  }, [fetchUsers]);

  // Grid ready handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api);
    
    // Auto-size columns when grid is ready
    setTimeout(() => {
      params.api.sizeColumnsToFit();
    }, 100);
  }, []);

  // Simple Badge component
  const Badge = ({ children, variant = "default", className = "" }: {
    children: React.ReactNode;
    variant?: "default" | "success" | "destructive" | "secondary" | "outline";
    className?: string;
  }) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium";
    const variantClasses = {
      default: "bg-primary/10 text-primary",
      success: "bg-green-100 text-green-800",
      destructive: "bg-red-100 text-red-800",
      secondary: "bg-gray-100 text-gray-800",
      outline: "border border-gray-300 text-gray-700"
    };

    return (
      <span className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
        {children}
      </span>
    );
  };

  // Status badge renderer
  const StatusBadgeRenderer = ({ value }: { value: boolean }) => {
    return (
      <Badge
        variant={value ? "success" : "destructive"}
        className="flex items-center gap-1"
      >
        {value ? (
          <>
            <CheckCircle className="h-3 w-3" />
            Active
          </>
        ) : (
          <>
            <XCircle className="h-3 w-3" />
            Inactive
          </>
        )}
      </Badge>
    );
  };

  // Email confirmed badge renderer
  const EmailConfirmedRenderer = ({ value }: { value: boolean }) => {
    return (
      <Badge 
        variant={value ? "success" : "secondary"}
        className="flex items-center gap-1"
      >
        {value ? (
          <>
            <CheckCircle className="h-3 w-3" />
            Confirmed
          </>
        ) : (
          <>
            <AlertCircle className="h-3 w-3" />
            Pending
          </>
        )}
      </Badge>
    );
  };

  // Roles renderer
  const RolesRenderer = ({ value }: { value: string[] }) => {
    if (!value || value.length === 0) {
      return <span className="text-muted-foreground">No roles</span>;
    }
    
    return (
      <div className="flex flex-wrap gap-1">
        {value.map((role, index) => (
          <Badge key={index} variant="outline" className="text-xs">
            {role}
          </Badge>
        ))}
      </div>
    );
  };

  // Date formatter
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  // Column definitions
  const columnDefs: ColDef[] = useMemo(() => [
    {
      headerName: 'User Name',
      field: 'userName',
      sortable: true,
      filter: true,
      pinned: 'left',
      width: 150,
      cellClass: 'font-medium'
    },
    {
      headerName: 'Full Name',
      valueGetter: (params) => `${params.data.firstName} ${params.data.lastName}`,
      sortable: true,
      filter: true,
      width: 180
    },
    {
      headerName: 'Email',
      field: 'email',
      sortable: true,
      filter: true,
      width: 200
    },
    {
      headerName: 'Status',
      field: 'isActive',
      sortable: true,
      filter: true,
      width: 100,
      cellRenderer: StatusBadgeRenderer
    },
    {
      headerName: 'Email Confirmed',
      field: 'emailConfirmed',
      sortable: true,
      filter: true,
      width: 140,
      cellRenderer: EmailConfirmedRenderer
    },
    {
      headerName: 'Phone',
      field: 'phoneNumber',
      sortable: true,
      filter: true,
      width: 140
    },
    {
      headerName: 'Roles',
      field: 'roles',
      sortable: false,
      filter: false,
      width: 200,
      cellRenderer: RolesRenderer
    },
    {
      headerName: 'MFA Enabled',
      field: 'isMFAEnabled',
      sortable: true,
      filter: true,
      width: 120,
      cellRenderer: ({ value }: { value: boolean }) => (
        <Badge variant={value ? "success" : "secondary"}>
          {value ? 'Yes' : 'No'}
        </Badge>
      )
    },
    {
      headerName: 'Created On',
      field: 'createdOn',
      sortable: true,
      filter: true,
      width: 160,
      valueFormatter: (params) => formatDate(params.value)
    },
    {
      headerName: 'Last Modified',
      field: 'lastModifiedOn',
      sortable: true,
      filter: true,
      width: 160,
      valueFormatter: (params) => formatDate(params.value)
    }
  ], []);

  return (
    <div className={cn('space-y-6 p-6', className)}>
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Users className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-foreground">Users ({users.length})</h1>
            <p className="text-sm text-muted-foreground">
              Manage and view user accounts
            </p>
          </div>
        </div>
        
        <Button
          onClick={handleRefresh}
          disabled={loading}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-1 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Users Grid */}
          <div className="ag-theme-alpine" style={{ height: '600px', width: '100%' }}>
            <AgGridReact
              rowData={users}
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              loading={loading}
              
              // Grid configuration
              defaultColDef={{
                sortable: true,
                filter: true,
                resizable: true,
                minWidth: 100
              }}
              
              // Pagination
              pagination={true}
              paginationPageSize={20}
              paginationPageSizeSelector={[10, 20, 50, 100]}
              
              // Other features
              animateRows={true}
              suppressCellFocus={true}
              rowSelection="multiple"
              
              // Styling
              headerHeight={40}
              rowHeight={45}
              
              // Row ID for better performance
              getRowId={(params) => params.data.id}
            />
          </div>
    </div>
  );
};
