import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Templates, Subscriptions, Settings } from './pages';
import { TenantApiTest } from './pages/TenantApiTest';
import { Layout } from './components/Layout';
import App from './App';
import 'bootstrap/dist/css/bootstrap.min.css';

export const AppRouter: React.FC = () => {
  return (
    <Router>
      <Routes>
        {/* Default route redirects to templates */}
        <Route path="/" element={<Navigate to="/templates" replace />} />
        
        {/* Templates list page */}
        <Route path="/templates" element={
          <Layout>
            <Templates />
          </Layout>
        } />

        {/* Subscriptions page */}
        <Route path="/subscriptions" element={
          <Layout>
            <Subscriptions />
          </Layout>
        } />

        {/* Settings page */}
        <Route path="/settings" element={
          <Layout>
            <Settings />
          </Layout>
        } />

        {/* Tenant API Test page */}
        <Route path="/tenant-api-test" element={
          <Layout>
            <TenantApiTest />
          </Layout>
        } />

        {/* Product builder page - without layout to maintain existing functionality */}
        <Route path="/product-builder" element={<App />} />
        
        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/templates" replace />} />
      </Routes>
    </Router>
  );
};
