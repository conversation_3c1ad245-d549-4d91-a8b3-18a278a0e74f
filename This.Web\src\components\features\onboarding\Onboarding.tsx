import {
  Building2,
  CheckCircle,
  <PERSON>,
  Shield,
  Users,
} from "lucide-react";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { OnboardingData, OnboardingProps } from "../../../types/onboarding";
import Step2UserImport from "./Step2UserImport";
import Step3RoleAssignment from "./Step3RoleAssignment";
const Onboarding: React.FC<OnboardingProps> = ({
  applicationDetails,
}) => {
  // Admin access control logic - check early to prevent unauthorized access
  const shouldShowAccessDenied = () => {
    // If onboarding is completed, allow access regardless of admin status
    if (applicationDetails.isOnboardCompleted) {
      return false;
    }

    // If onboarding is not completed and user is not admin, deny access
    if (!applicationDetails.isOnboardCompleted && !applicationDetails.isAdmin) {
      return true;
    }

    // Allow access for admins or when onboarding is completed
    return false;
  };

  // Render access denied UI for non-admin users when onboarding is incomplete
  const renderAccessDenied = () => (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Lock className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="text-xl font-bold text-gray-900 mb-4">Access Restricted</h1>
          <p className="text-gray-600 mb-6 text-sm leading-relaxed">
            Only administrators can access the onboarding process. Please contact your system administrator.
          </p>
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div className="text-xs text-gray-500 space-y-1">
              <div><strong>Application:</strong> {applicationDetails.name}</div>
              <div><strong>Tenant:</strong> {applicationDetails.tenantId}</div>
              <div><strong>Status:</strong> Onboarding Required</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Early return for access denied - prevents any child components from rendering
  if (shouldShowAccessDenied()) {
    return renderAccessDenied();
  }

  // Determine initial step based on application details flags
  const getInitialStep = () => {
    // Check flags to determine which step to start at
    if (applicationDetails.isRoleAssigned || applicationDetails.isOnboardCompleted) {
      // If roles are already assigned or onboarding is completed, redirect will happen in useEffect
      return 3;
    } else if (applicationDetails.isUserImported) {
      // Users are imported, go to role assignment
      return 3;
    } else {
      // Users not imported yet, go to user import
      return 2;
    }
  };

  const [currentStep, setCurrentStep] = useState(getInitialStep());
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    tenantId: applicationDetails.tenantId,
    users: [],
    roleAssignments: {},
  });

  const navigate = useNavigate();

  // Handle redirect logic when component mounts with existing application details
  React.useEffect(() => {
    if (applicationDetails.isOnboardCompleted) {
      navigate("/home");
      return;
    }

    if (applicationDetails.isRoleAssigned) {
      navigate("/home");
      return;
    }
  }, [applicationDetails, navigate]);

  const steps = [
    {
      number: 1,
      title: "Step 1",
      description: "Check Tenant",
      icon: Building2,
    },
    { number: 2, title: "Step 2", description: "Import Users", icon: Users },
    { number: 3, title: "Step 3", description: "Assign Roles", icon: Shield },
  ];

  const handleStepComplete = (step: number, data: Record<string, unknown>) => {
    if (step === 2) {
      setOnboardingData((prev: OnboardingData) => ({
        ...prev,
        users: data as unknown as OnboardingData["users"],
      }));

      // Check if we should skip to home if onboarding is completed or roles are already assigned
      if (applicationDetails.isOnboardCompleted || applicationDetails.isRoleAssigned) {
        navigate("/home");
      } else {
        // Proceed to step 3 if onboarding is not completed and roles are not assigned
        setCurrentStep(3);
      }
    } else if (step === 3) {
      // Data from Step3RoleAssignment is the role assignments
      setOnboardingData((prev: OnboardingData) => ({
        ...prev,
        roleAssignments: data as OnboardingData["roleAssignments"],
      }));
      // Navigate to home on completion
      navigate("/home");
    }
  };



  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-12">
      {steps.map((step, index) => {
        const IconComponent = step.icon;
        const isCompleted = step.number < currentStep;
        const isCurrent = step.number === currentStep;

        return (
          <div key={step.number} className="flex items-center">
            <div className="flex flex-col items-center">
              {/* Step Circle */}
              <div
                className={`relative flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-500 ${isCompleted
                    ? "step-completed border-primary-600 text-white"
                    : isCurrent
                      ? "step-current border-primary-500 text-white"
                      : "bg-white border-gray-300 text-gray-400 hover:border-gray-400 hover:bg-gray-50"
                  }`}
              >
                {isCompleted ? (
                  <CheckCircle className="w-6 h-6" />
                ) : (
                  <IconComponent className="w-6 h-6" />
                )}
              </div>

              {/* Step Label */}
              <div className="mt-4 text-center">
                <div className="text-xxs font-medium text-gray-500">
                  {step.title}
                </div>
                <div className="text-xs font-medium">{step.description}</div>
                {isCompleted && (
                  <div className="text-xxs text-primary-700 mt-1 font-medium">
                    ✓ Completed
                  </div>
                )}
                {isCurrent && (
                  <div className="text-xxs text-yellow-500 mt-1 font-medium">
                    In Progress
                  </div>
                )}
                {!isCompleted && !isCurrent && (
                  <div className="text-xxs mt-1 font-medium">&nbsp;</div>
                )}
              </div>
            </div>

            {/* Connecting Line */}
            {index < steps.length - 1 && (
              <div className="flex items-center mx-8">
                <div className="flex space-x-1">
                  {Array.from({ length: 8 }).map((_, dotIndex) => (
                    <div
                      key={`step-${index}-dot-${dotIndex}`}
                      className={`w-1.5 h-1.5 rounded-full transition-all duration-500 ${isCompleted ? "bg-primary-600 shadow-sm" : "bg-gray-300"
                        }`}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  const renderCurrentStep = () => {
    // Skip to home if roles are already assigned
    if (applicationDetails.isRoleAssigned) {
      navigate("/home");
      return null;
    }

    switch (currentStep) {
      case 2:
        return (
          <Step2UserImport
            tenantId={applicationDetails.tenantId}
            applicationDetails={applicationDetails}
            onComplete={(data: any) => handleStepComplete(2, data)}
          />
        );
      case 3:
        return (
          <Step3RoleAssignment
            users={onboardingData.users}
            tenantId={applicationDetails.tenantId}
            applicationDetails={applicationDetails}
            onComplete={(data: any) => handleStepComplete(3, data)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-xl font-bold text-gray-900">
            {applicationDetails.name} Onboarding
          </h1>
          <p className="text-base text-gray-600">
            Setting up tenant: {applicationDetails.tenantId}
          </p>
        </div>

        {renderStepIndicator()}

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
